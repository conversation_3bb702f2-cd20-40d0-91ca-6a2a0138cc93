#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试交易逻辑

检查为什么0.24%阈值的交易频率与预期不符
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def debug_trading_logic():
    """调试交易逻辑"""
    
    print("="*80)
    print("    Debug Trading Logic - Why Low Trading Frequency?")
    print("="*80)
    
    # 加载数据
    data_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')
    
    try:
        data = pd.read_parquet(data_path)
        data['time_key'] = pd.to_datetime(data['time_key'])
        print(f"✅ Data loaded: {len(data)} records")
        
        # 过滤有港股通数据的记录
        hsgt_data = data[data['hsgt_holding_ratio'].notna()].copy()
        print(f"✅ HSGT data: {len(hsgt_data)} records")
        
    except Exception as e:
        print(f"❌ Failed to load data: {e}")
        return
    
    # 计算港股通变化和次日收益
    print("\n📊 Calculating HSGT changes and next day returns...")
    
    results = []
    
    for code in hsgt_data['code'].unique():
        stock_data = hsgt_data[hsgt_data['code'] == code].copy()
        stock_data = stock_data.sort_values('time_key')
        
        if len(stock_data) < 2:
            continue
        
        # 计算1天港股通持股比例变化
        stock_data['hsgt_ratio_change_1d'] = stock_data['hsgt_holding_ratio'].diff()
        
        # 计算次日收益率
        stock_data['next_day_return'] = stock_data['close'].shift(-1) / stock_data['close'] - 1
        
        results.append(stock_data)
    
    data_with_changes = pd.concat(results, ignore_index=True)
    print(f"✅ Calculated changes for {len(data_with_changes)} records")
    
    # 分析最近几个交易日的情况
    print(f"\n🔍 Analyzing Recent Trading Days:")
    
    recent_dates = sorted(data_with_changes['time_key'].unique())[-10:]  # 最近10个交易日
    
    threshold = 0.24
    
    for date in recent_dates:
        daily_data = data_with_changes[data_with_changes['time_key'] == date]
        
        # 检查有多少股票满足条件
        condition1 = daily_data['hsgt_ratio_change_1d'].notna()
        condition2 = daily_data['next_day_return'].notna()
        condition3 = daily_data['hsgt_ratio_change_1d'] >= threshold
        
        all_conditions = condition1 & condition2 & condition3
        
        print(f"\n📅 {date.strftime('%Y-%m-%d')}:")
        print(f"   Total stocks: {len(daily_data)}")
        print(f"   Has HSGT change: {condition1.sum()}")
        print(f"   Has next day return: {condition2.sum()}")
        print(f"   HSGT change >= {threshold}%: {condition3.sum()}")
        print(f"   Meets all conditions: {all_conditions.sum()}")
        
        # 显示满足条件的股票
        valid_stocks = daily_data[all_conditions]
        if not valid_stocks.empty:
            print(f"   Valid stocks for trading:")
            for _, stock in valid_stocks.head(5).iterrows():  # 显示前5只
                print(f"     {stock['code']}: HSGT +{stock['hsgt_ratio_change_1d']:.3f}%, Next day: {stock['next_day_return']*100:+.2f}%")
        
        # 检查为什么next_day_return缺失
        missing_next_day = daily_data[condition1 & condition3 & ~condition2]
        if not missing_next_day.empty:
            print(f"   ⚠️  {len(missing_next_day)} stocks missing next day return (likely last trading day)")
    
    # 分析整体数据质量
    print(f"\n📊 Overall Data Quality Analysis:")
    
    total_records = len(data_with_changes)
    has_hsgt_change = data_with_changes['hsgt_ratio_change_1d'].notna().sum()
    has_next_return = data_with_changes['next_day_return'].notna().sum()
    has_both = (data_with_changes['hsgt_ratio_change_1d'].notna() & 
                data_with_changes['next_day_return'].notna()).sum()
    
    print(f"   Total records: {total_records:,}")
    print(f"   Has HSGT change: {has_hsgt_change:,} ({has_hsgt_change/total_records*100:.1f}%)")
    print(f"   Has next day return: {has_next_return:,} ({has_next_return/total_records*100:.1f}%)")
    print(f"   Has both: {has_both:,} ({has_both/total_records*100:.1f}%)")
    
    # 分析为什么next_day_return缺失
    print(f"\n🔍 Why Next Day Return is Missing:")
    
    # 按日期分组，检查每个日期的下一个交易日是否存在
    dates_with_next = []
    dates_without_next = []
    
    all_dates = sorted(data_with_changes['time_key'].unique())
    
    for i, date in enumerate(all_dates[:-1]):  # 排除最后一天
        next_date = all_dates[i + 1]
        current_stocks = set(data_with_changes[data_with_changes['time_key'] == date]['code'])
        next_stocks = set(data_with_changes[data_with_changes['time_key'] == next_date]['code'])
        
        overlap = len(current_stocks & next_stocks)
        total_current = len(current_stocks)
        
        if overlap / total_current > 0.8:  # 如果80%以上的股票在下一天也有数据
            dates_with_next.append(date)
        else:
            dates_without_next.append(date)
    
    print(f"   Dates with good next-day coverage: {len(dates_with_next)}")
    print(f"   Dates with poor next-day coverage: {len(dates_without_next)}")
    
    if dates_without_next:
        print(f"   Problematic dates: {[d.strftime('%Y-%m-%d') for d in dates_without_next[-5:]]}")
    
    # 重新计算有效的交易机会
    print(f"\n🎯 Recalculating Valid Trading Opportunities:")
    
    valid_trading_data = data_with_changes[
        (data_with_changes['hsgt_ratio_change_1d'].notna()) &
        (data_with_changes['next_day_return'].notna())
    ].copy()
    
    print(f"   Valid trading records: {len(valid_trading_data):,}")
    
    # 按阈值分析
    thresholds = [0.0, 0.1, 0.24, 0.5, 1.0]
    
    for threshold in thresholds:
        above_threshold = (valid_trading_data['hsgt_ratio_change_1d'] >= threshold).sum()
        percentage = above_threshold / len(valid_trading_data) * 100
        
        # 估算每日交易机会
        trading_days = len(valid_trading_data['time_key'].unique())
        avg_per_day = above_threshold / trading_days
        
        print(f"   Threshold >= {threshold:4.1f}%: {above_threshold:,} opportunities ({percentage:.2f}%), ~{avg_per_day:.1f} per day")
    
    # 验证我们的策略应该有多少交易
    print(f"\n🔢 Expected vs Actual Trading Frequency:")
    
    threshold_024_opportunities = (valid_trading_data['hsgt_ratio_change_1d'] >= 0.24).sum()
    trading_days = len(valid_trading_data['time_key'].unique())
    expected_trades = threshold_024_opportunities  # 每个机会一笔交易
    
    print(f"   Expected trades with 0.24% threshold: {expected_trades}")
    print(f"   Trading days in data: {trading_days}")
    print(f"   Average trades per day: {expected_trades / trading_days:.2f}")
    
    # 检查实际的交易日志
    try:
        trades_file = os.path.join(os.path.dirname(__file__), 'daily_rotation_results', 'daily_rotation_trades_20250903_160013.csv')
        actual_trades = pd.read_csv(trades_file)
        print(f"   Actual trades in log: {len(actual_trades)}")
        print(f"   Difference: {expected_trades - len(actual_trades)} trades")
        
        if expected_trades != len(actual_trades):
            print(f"   ⚠️  There's a discrepancy! Need to investigate further.")
    except:
        print(f"   Could not load actual trading log for comparison")

def main():
    """主函数"""
    
    debug_trading_logic()
    
    print(f"\n{'='*80}")
    print("    Debugging Conclusion")
    print(f"{'='*80}")
    print("🔍 The issue is likely in the data preparation or filtering logic")
    print("📊 Need to check why next_day_return is missing for many records")
    print("🎯 This explains the lower-than-expected trading frequency")
    print("💡 The threshold calculation itself appears to be correct")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
