#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通日轮换策略

每日买入港股通持股增加最多的股票，持有1天后卖出
基于发现：1天港股通变化与1天收益相关性最强(r=0.0214)
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置图表样式，使用英文避免乱码
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

class HSGTDailyRotationStrategy:
    """港股通日轮换策略回测器"""
    
    def __init__(self, initial_capital: float = 1000000):
        """
        初始化策略
        
        Args:
            initial_capital: 初始资金
        """
        self.initial_capital = initial_capital
        self.data = None
        
    def load_data(self, data_path: str):
        """加载港股数据"""
        try:
            self.data = pd.read_parquet(data_path)
            self.data['time_key'] = pd.to_datetime(self.data['time_key'])
            print(f"Data loaded: {len(self.data)} records")
            
            # 过滤有港股通数据的记录
            self.data = self.data[self.data['hsgt_holding_ratio'].notna()].copy()
            print(f"Records with HSGT data: {len(self.data)}")
            
            return True
        except Exception as e:
            print(f"Failed to load data: {e}")
            return False
    
    def calculate_daily_hsgt_changes(self):
        """计算每日港股通变化"""
        print("Calculating daily HSGT changes...")
        
        results = []
        
        for code in self.data['code'].unique():
            stock_data = self.data[self.data['code'] == code].copy()
            stock_data = stock_data.sort_values('time_key')
            
            if len(stock_data) < 2:
                continue
            
            # 计算1天港股通持股比例变化（绝对值）
            stock_data['hsgt_ratio_change_1d'] = stock_data['hsgt_holding_ratio'].diff()
            
            # 计算1天港股通持股市值变化（如果有数据）
            if 'hsgt_holding_value' in stock_data.columns:
                stock_data['hsgt_value_change_1d'] = stock_data['hsgt_holding_value'].diff()
            else:
                # 用持股比例变化 * 市值估算
                stock_data['market_cap'] = stock_data['close'] * stock_data.get('volume', 1000000)  # 简化估算
                stock_data['hsgt_value_change_1d'] = (
                    stock_data['hsgt_ratio_change_1d'] / 100 * stock_data['market_cap']
                )
            
            # 计算次日收益率
            stock_data['next_day_return'] = stock_data['close'].shift(-1) / stock_data['close'] - 1
            
            results.append(stock_data)
        
        self.data_with_changes = pd.concat(results, ignore_index=True)
        print(f"HSGT changes calculated for {len(self.data_with_changes)} records")
        
        return self.data_with_changes
    
    def daily_rotation_backtest(self, top_n: int = 1, selection_method: str = 'absolute_ratio'):
        """
        执行日轮换策略回测
        
        Args:
            top_n: 每日选择的股票数量
            selection_method: 选择方法 ('absolute_ratio', 'absolute_value', 'percentage_ratio')
        """
        print(f"\nExecuting daily rotation strategy...")
        print(f"Parameters: Top {top_n} stocks, selection method: {selection_method}")
        
        # 准备数据
        data = self.data_with_changes.copy()
        data = data.sort_values(['time_key', 'code'])
        
        # 初始化回测变量
        portfolio_value = self.initial_capital
        trades = []
        portfolio_history = []
        
        # 按日期进行回测
        trading_dates = sorted(data['time_key'].unique())
        
        for i, current_date in enumerate(trading_dates[:-1]):  # 排除最后一天（无法计算次日收益）
            current_data = data[data['time_key'] == current_date]
            
            # 过滤有效数据（有港股通变化数据且有次日收益数据）
            valid_data = current_data[
                (current_data['hsgt_ratio_change_1d'].notna()) &
                (current_data['next_day_return'].notna())
            ].copy()
            
            if valid_data.empty:
                # 如果没有有效数据，记录当日组合价值（保持不变）
                portfolio_history.append({
                    'date': current_date,
                    'portfolio_value': portfolio_value,
                    'daily_return': 0,
                    'selected_stocks': 0,
                    'avg_hsgt_change': 0
                })
                continue
            
            # 根据选择方法排序
            if selection_method == 'absolute_ratio':
                # 按港股通持股比例绝对变化排序
                valid_data = valid_data.sort_values('hsgt_ratio_change_1d', ascending=False)
                selection_col = 'hsgt_ratio_change_1d'
            elif selection_method == 'absolute_value':
                # 按港股通持股市值绝对变化排序
                valid_data = valid_data.sort_values('hsgt_value_change_1d', ascending=False)
                selection_col = 'hsgt_value_change_1d'
            elif selection_method == 'percentage_ratio':
                # 按港股通持股比例百分比变化排序
                valid_data['hsgt_ratio_pct_change_1d'] = valid_data['hsgt_holding_ratio'].pct_change()
                valid_data = valid_data.sort_values('hsgt_ratio_pct_change_1d', ascending=False)
                selection_col = 'hsgt_ratio_pct_change_1d'
            
            # 选择前N只股票
            selected_stocks = valid_data.head(top_n)
            
            if selected_stocks.empty:
                portfolio_history.append({
                    'date': current_date,
                    'portfolio_value': portfolio_value,
                    'daily_return': 0,
                    'selected_stocks': 0,
                    'avg_hsgt_change': 0
                })
                continue
            
            # 计算当日收益（等权重投资）
            daily_returns = selected_stocks['next_day_return'].values
            avg_daily_return = np.mean(daily_returns)
            
            # 更新组合价值
            new_portfolio_value = portfolio_value * (1 + avg_daily_return)
            daily_pnl = new_portfolio_value - portfolio_value
            
            # 记录交易
            for _, stock in selected_stocks.iterrows():
                position_size = portfolio_value / top_n
                shares = position_size / stock['close']
                
                trades.append({
                    'date': current_date,
                    'code': stock['code'],
                    'name': stock.get('name', ''),
                    'entry_price': stock['close'],
                    'position_size': position_size,
                    'shares': shares,
                    'hsgt_change': stock[selection_col],
                    'next_day_return': stock['next_day_return'],
                    'pnl': position_size * stock['next_day_return']
                })
            
            # 记录组合历史
            portfolio_history.append({
                'date': current_date,
                'portfolio_value': new_portfolio_value,
                'daily_return': avg_daily_return * 100,
                'selected_stocks': len(selected_stocks),
                'avg_hsgt_change': selected_stocks[selection_col].mean(),
                'daily_pnl': daily_pnl
            })
            
            portfolio_value = new_portfolio_value
        
        # 转换为DataFrame
        self.trades_df = pd.DataFrame(trades)
        self.portfolio_df = pd.DataFrame(portfolio_history)
        
        return self.trades_df, self.portfolio_df
    
    def calculate_performance_metrics(self):
        """计算绩效指标"""
        if self.portfolio_df.empty:
            return {}
        
        portfolio_values = self.portfolio_df['portfolio_value'].values
        daily_returns = self.portfolio_df['daily_return'].values / 100
        
        # 基本指标
        total_return = (portfolio_values[-1] / self.initial_capital - 1) * 100
        
        # 年化收益率
        trading_days = len(portfolio_values)
        annualized_return = ((portfolio_values[-1] / self.initial_capital) ** (252 / trading_days) - 1) * 100
        
        # 风险指标
        volatility = np.std(daily_returns) * np.sqrt(252) * 100
        sharpe_ratio = (annualized_return - 3) / volatility if volatility > 0 else 0
        
        # 最大回撤
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (portfolio_values - peak) / peak
        max_drawdown = np.min(drawdown) * 100
        
        # 胜率
        winning_days = len(daily_returns[daily_returns > 0])
        total_days = len(daily_returns[daily_returns != 0])
        win_rate = (winning_days / total_days * 100) if total_days > 0 else 0
        
        # 平均日收益
        avg_daily_return = np.mean(daily_returns) * 100
        
        metrics = {
            'total_return_pct': total_return,
            'annualized_return_pct': annualized_return,
            'volatility_pct': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown,
            'win_rate_pct': win_rate,
            'avg_daily_return_pct': avg_daily_return,
            'total_trading_days': total_days,
            'final_value': portfolio_values[-1]
        }
        
        return metrics
    
    def create_benchmark(self):
        """创建基准：等权重港股指数"""
        print("Creating equal-weight benchmark...")
        
        benchmark_data = []
        trading_dates = sorted(self.data_with_changes['time_key'].unique())
        
        for i, date in enumerate(trading_dates[:-1]):
            current_data = self.data_with_changes[self.data_with_changes['time_key'] == date]
            valid_data = current_data[current_data['next_day_return'].notna()]
            
            if not valid_data.empty:
                avg_return = valid_data['next_day_return'].mean()
                benchmark_data.append({
                    'date': date,
                    'daily_return': avg_return * 100
                })
        
        self.benchmark_df = pd.DataFrame(benchmark_data)
        
        if not self.benchmark_df.empty:
            # 计算累计收益
            self.benchmark_df['cumulative_return'] = (1 + self.benchmark_df['daily_return'] / 100).cumprod()
            self.benchmark_df['benchmark_value'] = self.initial_capital * self.benchmark_df['cumulative_return']
        
        return self.benchmark_df
    
    def plot_performance(self, output_dir: str):
        """绘制策略表现"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('HSGT Daily Rotation Strategy Performance', fontsize=16, fontweight='bold')
        
        # 1. 累计收益对比
        ax1 = axes[0, 0]
        ax1.plot(self.portfolio_df['date'], self.portfolio_df['portfolio_value'], 
                linewidth=2, label='Daily Rotation Strategy', color='blue')
        
        if hasattr(self, 'benchmark_df') and not self.benchmark_df.empty:
            ax1.plot(self.benchmark_df['date'], self.benchmark_df['benchmark_value'], 
                    linewidth=2, label='Equal-Weight Benchmark', color='red', alpha=0.7)
        
        ax1.axhline(y=self.initial_capital, color='gray', linestyle='--', alpha=0.5)
        ax1.set_title('Cumulative Performance')
        ax1.set_ylabel('Portfolio Value')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)
        
        # 2. 日收益分布
        ax2 = axes[0, 1]
        daily_returns = self.portfolio_df['daily_return'].values
        ax2.hist(daily_returns, bins=50, alpha=0.7, edgecolor='black')
        ax2.axvline(x=0, color='red', linestyle='--', alpha=0.7)
        ax2.axvline(x=daily_returns.mean(), color='green', linestyle='-', alpha=0.7, 
                   label=f'Mean: {daily_returns.mean():.3f}%')
        ax2.set_title('Daily Return Distribution')
        ax2.set_xlabel('Daily Return (%)')
        ax2.set_ylabel('Frequency')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 港股通变化与收益散点图
        ax3 = axes[1, 0]
        if not self.trades_df.empty:
            ax3.scatter(self.trades_df['hsgt_change'], self.trades_df['next_day_return'] * 100, 
                       alpha=0.6, s=20)
            
            # 添加趋势线
            x = self.trades_df['hsgt_change'].values
            y = self.trades_df['next_day_return'].values * 100
            valid_mask = np.isfinite(x) & np.isfinite(y)
            if valid_mask.sum() > 1:
                z = np.polyfit(x[valid_mask], y[valid_mask], 1)
                p = np.poly1d(z)
                ax3.plot(x[valid_mask], p(x[valid_mask]), "r--", alpha=0.8)
                
                # 计算相关系数
                corr = np.corrcoef(x[valid_mask], y[valid_mask])[0, 1]
                ax3.text(0.05, 0.95, f'Correlation: {corr:.4f}', 
                        transform=ax3.transAxes, bbox=dict(boxstyle="round", facecolor='wheat'))
            
            ax3.set_title('HSGT Change vs Next Day Return')
            ax3.set_xlabel('HSGT Change')
            ax3.set_ylabel('Next Day Return (%)')
            ax3.grid(True, alpha=0.3)
        
        # 4. 滚动夏普比率
        ax4 = axes[1, 1]
        if len(self.portfolio_df) > 20:
            rolling_window = 20
            rolling_returns = self.portfolio_df['daily_return'].rolling(window=rolling_window).mean()
            rolling_vol = self.portfolio_df['daily_return'].rolling(window=rolling_window).std()
            rolling_sharpe = (rolling_returns - 0.012) / rolling_vol * np.sqrt(252)  # 假设无风险利率3%
            
            ax4.plot(self.portfolio_df['date'][rolling_window-1:], rolling_sharpe[rolling_window-1:], 
                    linewidth=2, color='purple')
            ax4.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
            ax4.set_title(f'{rolling_window}-Day Rolling Sharpe Ratio')
            ax4.set_ylabel('Sharpe Ratio')
            ax4.grid(True, alpha=0.3)
            ax4.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        # 保存图片
        filename = os.path.join(output_dir, 'daily_rotation_strategy_performance.png')
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 Daily rotation strategy chart saved: {filename}")
        plt.show()

def main():
    """主函数"""
    print("="*80)
    print("    HSGT Daily Rotation Strategy Backtest")
    print("="*80)
    
    # 设置路径
    script_dir = os.path.dirname(__file__)
    project_root = os.path.abspath(os.path.join(script_dir, '..'))
    data_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')
    output_dir = os.path.join(script_dir, 'daily_rotation_results')
    os.makedirs(output_dir, exist_ok=True)
    
    if not os.path.exists(data_path):
        print(f"Error: Data file not found {data_path}")
        return
    
    # 创建策略实例
    strategy = HSGTDailyRotationStrategy(initial_capital=1000000)
    
    # 加载数据
    if not strategy.load_data(data_path):
        return
    
    # 计算港股通变化
    strategy.calculate_daily_hsgt_changes()
    
    # 创建基准
    strategy.create_benchmark()
    
    print("\n" + "="*60)
    print("    Strategy Details")
    print("="*60)
    print("📋 Strategy: Daily Rotation based on HSGT Holdings Change")
    print("🎯 Logic: Buy stocks with highest 1-day HSGT increase, hold for 1 day")
    print("💼 Position: Equal weight allocation")
    print("🔄 Frequency: Daily rebalancing")
    print("⚠️  Assumptions: No transaction costs, perfect execution")
    print("="*60)
    
    # 测试不同的选择方法
    methods = [
        ('absolute_ratio', 'Absolute Ratio Change'),
        ('absolute_value', 'Absolute Value Change'),
        ('percentage_ratio', 'Percentage Ratio Change')
    ]
    
    results_summary = []
    
    for method, method_name in methods:
        print(f"\n🚀 Testing {method_name}...")
        
        # 执行回测
        trades_df, portfolio_df = strategy.daily_rotation_backtest(
            top_n=1, 
            selection_method=method
        )
        
        # 计算绩效
        metrics = strategy.calculate_performance_metrics()
        
        # 保存结果
        results_summary.append({
            'method': method_name,
            **metrics
        })
        
        print(f"   Total Return: {metrics['total_return_pct']:.2f}%")
        print(f"   Annualized Return: {metrics['annualized_return_pct']:.2f}%")
        print(f"   Sharpe Ratio: {metrics['sharpe_ratio']:.2f}")
        print(f"   Win Rate: {metrics['win_rate_pct']:.2f}%")
        
        # 保存详细结果（只保存最后一个方法的详细结果）
        if method == 'absolute_ratio':
            current_date = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            trades_path = os.path.join(output_dir, f'daily_rotation_trades_{current_date}.csv')
            trades_df.to_csv(trades_path, index=False, encoding='utf-8-sig')
            
            portfolio_path = os.path.join(output_dir, f'daily_rotation_portfolio_{current_date}.csv')
            portfolio_df.to_csv(portfolio_path, index=False, encoding='utf-8-sig')
            
            # 生成图表
            strategy.plot_performance(output_dir)
    
    # 打印汇总比较
    print("\n" + "="*80)
    print("                Method Comparison Summary")
    print("="*80)
    
    summary_df = pd.DataFrame(results_summary)
    
    print(f"{'Method':<25} {'Total Return':<12} {'Ann. Return':<12} {'Sharpe':<8} {'Win Rate':<10}")
    print("-" * 80)
    
    for _, row in summary_df.iterrows():
        print(f"{row['method']:<25} {row['total_return_pct']:>10.2f}% {row['annualized_return_pct']:>10.2f}% "
              f"{row['sharpe_ratio']:>6.2f} {row['win_rate_pct']:>8.2f}%")
    
    # 保存汇总结果
    summary_path = os.path.join(output_dir, f'method_comparison_{current_date}.csv')
    summary_df.to_csv(summary_path, index=False, encoding='utf-8-sig')
    print(f"\n💾 Method comparison saved: {summary_path}")
    
    print("="*80)

if __name__ == "__main__":
    main()
