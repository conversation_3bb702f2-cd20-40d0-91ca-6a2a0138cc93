#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通持股比例变化增强策略回测

详细的策略说明和基准比较回测
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置图表样式，使用英文避免乱码
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

class HSGTEnhancedStrategyBacktester:
    """港股通增强策略回测器"""
    
    def __init__(self, initial_capital: float = 1000000):
        """
        初始化回测器
        
        Args:
            initial_capital: 初始资金
        """
        self.initial_capital = initial_capital
        self.results = {}
        
    def print_strategy_details(self):
        """打印详细的策略说明"""
        print("\n" + "="*80)
        print("                    HSGT Strategy Details")
        print("="*80)
        
        print("📋 Strategy Overview:")
        print("   Name: HSGT Holdings Change Momentum Strategy")
        print("   Type: Short-term momentum strategy based on institutional flow")
        print("   Universe: Hong Kong stocks with HSGT (Southbound Connect) data")
        
        print("\n🎯 Signal Generation:")
        print("   1. Calculate 5-day HSGT holdings ratio change for each stock")
        print("   2. Rank stocks by HSGT change magnitude (rolling 20-day window)")
        print("   3. BUY Signal: HSGT change >= 80th percentile (top 20%)")
        print("   4. SELL Signal: HSGT change <= 20th percentile (bottom 20%)")
        print("   5. NO Signal: HSGT change between 20th-80th percentile")
        
        print("\n💼 Portfolio Management:")
        print("   - Maximum positions: 10 stocks")
        print("   - Position sizing: Equal weight (1/10 of capital per position)")
        print("   - Holding period: 5 trading days (forced exit)")
        print("   - Rebalancing: Daily signal check and position management")
        
        print("\n🔄 Trading Rules:")
        print("   - Entry: Buy at market close when signal = 1")
        print("   - Exit: Sell at market close after 5 days OR when signal = -1")
        print("   - Priority: Stocks with highest HSGT increase get priority")
        print("   - Cash management: Remaining cash earns 0% (conservative)")
        
        print("\n⚠️  Strategy Assumptions:")
        print("   - No transaction costs or slippage")
        print("   - Perfect execution at closing prices")
        print("   - No market impact from trades")
        print("   - Sufficient liquidity for all trades")
        
        print("="*80)
    
    def load_signal_data(self, signal_file_path: str):
        """加载交易信号数据"""
        try:
            self.signal_data = pd.read_csv(signal_file_path)
            self.signal_data['time_key'] = pd.to_datetime(self.signal_data['time_key'])
            print(f"Signal data loaded: {len(self.signal_data)} records")
            return True
        except Exception as e:
            print(f"Failed to load signal data: {e}")
            return False
    
    def create_benchmark_data(self):
        """创建基准数据（等权重港股指数）"""
        print("Creating equal-weight HK stocks benchmark...")
        
        # 使用所有有数据的股票创建等权重基准
        benchmark_data = []
        
        # 按日期分组计算等权重收益
        for date in sorted(self.signal_data['time_key'].unique()):
            daily_data = self.signal_data[self.signal_data['time_key'] == date]
            
            if not daily_data.empty and 'close' in daily_data.columns:
                # 计算当日平均收益率（等权重）
                if len(daily_data) > 0:
                    # 使用前一日数据计算收益率
                    prev_date_data = self.signal_data[self.signal_data['time_key'] < date]
                    if not prev_date_data.empty:
                        # 获取前一个交易日
                        prev_date = prev_date_data['time_key'].max()
                        prev_daily_data = self.signal_data[self.signal_data['time_key'] == prev_date]
                        
                        # 计算收益率
                        returns = []
                        for _, row in daily_data.iterrows():
                            code = row['code']
                            current_price = row['close']
                            
                            prev_row = prev_daily_data[prev_daily_data['code'] == code]
                            if not prev_row.empty:
                                prev_price = prev_row['close'].iloc[0]
                                if prev_price > 0:
                                    daily_return = (current_price / prev_price - 1) * 100
                                    returns.append(daily_return)
                        
                        if returns:
                            avg_return = np.mean(returns)
                        else:
                            avg_return = 0
                    else:
                        avg_return = 0
                    
                    benchmark_data.append({
                        'date': date,
                        'daily_return': avg_return
                    })
        
        self.benchmark_df = pd.DataFrame(benchmark_data)
        
        # 计算累计收益
        if not self.benchmark_df.empty:
            self.benchmark_df['cumulative_return'] = (1 + self.benchmark_df['daily_return'] / 100).cumprod()
            self.benchmark_df['benchmark_value'] = self.initial_capital * self.benchmark_df['cumulative_return']
        
        print(f"Benchmark created with {len(self.benchmark_df)} data points")
        return self.benchmark_df
    
    def enhanced_strategy_backtest(self, holding_days: int = 5, max_positions: int = 10):
        """
        增强策略回测
        
        Args:
            holding_days: 持仓天数
            max_positions: 最大持仓数量
        """
        print(f"\nExecuting enhanced strategy backtest...")
        print(f"Parameters: {holding_days}-day holding, max {max_positions} positions")
        
        # 准备数据
        data = self.signal_data.copy()
        data = data.sort_values(['time_key', 'code'])
        
        # 初始化回测变量
        portfolio_value = self.initial_capital
        cash = self.initial_capital
        positions = {}  # {code: {'shares': shares, 'entry_price': price, 'entry_date': date}}
        trades = []
        portfolio_history = []
        
        # 按日期进行回测
        trading_dates = sorted(data['time_key'].unique())
        
        for current_date in trading_dates:
            daily_data = data[data['time_key'] == current_date]
            
            # 检查是否需要平仓（持仓超过指定天数）
            positions_to_close = []
            for code, position in positions.items():
                days_held = (current_date - position['entry_date']).days
                if days_held >= holding_days:
                    positions_to_close.append(code)
            
            # 平仓
            for code in positions_to_close:
                position = positions[code]
                current_price_data = daily_data[daily_data['code'] == code]
                
                if not current_price_data.empty:
                    exit_price = current_price_data['close'].iloc[0]
                    shares = position['shares']
                    exit_value = shares * exit_price
                    
                    # 计算收益
                    entry_value = shares * position['entry_price']
                    pnl = exit_value - entry_value
                    pnl_pct = (exit_price / position['entry_price'] - 1) * 100
                    
                    # 记录交易
                    trades.append({
                        'code': code,
                        'action': 'SELL',
                        'date': current_date,
                        'price': exit_price,
                        'shares': shares,
                        'value': exit_value,
                        'pnl': pnl,
                        'pnl_pct': pnl_pct,
                        'holding_days': days_held,
                        'exit_reason': 'TIME_LIMIT'
                    })
                    
                    cash += exit_value
                    del positions[code]
            
            # 寻找买入信号
            buy_signals = daily_data[daily_data['signal'] == 1]
            
            # 按港股通变化幅度排序，优先买入变化最大的
            if not buy_signals.empty and 'hsgt_ratio_change_5d' in buy_signals.columns:
                buy_signals = buy_signals.sort_values('hsgt_ratio_change_5d', ascending=False)
            
            # 执行买入
            for _, signal_row in buy_signals.iterrows():
                code = signal_row['code']
                
                # 检查是否已持仓或达到最大持仓数
                if code in positions or len(positions) >= max_positions:
                    continue
                
                # 检查是否有足够现金
                price = signal_row['close']
                position_size = cash / max_positions  # 等权重分配
                shares = int(position_size / price)
                
                if shares > 0 and shares * price <= cash:
                    # 买入
                    entry_value = shares * price
                    cash -= entry_value
                    
                    positions[code] = {
                        'shares': shares,
                        'entry_price': price,
                        'entry_date': current_date
                    }
                    
                    # 记录交易
                    trades.append({
                        'code': code,
                        'action': 'BUY',
                        'date': current_date,
                        'price': price,
                        'shares': shares,
                        'value': entry_value,
                        'pnl': 0,
                        'pnl_pct': 0,
                        'holding_days': 0,
                        'exit_reason': 'ENTRY'
                    })
            
            # 计算当日组合价值
            position_value = 0
            for code, position in positions.items():
                current_price_data = daily_data[daily_data['code'] == code]
                if not current_price_data.empty:
                    current_price = current_price_data['close'].iloc[0]
                    position_value += position['shares'] * current_price
                else:
                    # 如果没有当日数据，使用入场价格
                    position_value += position['shares'] * position['entry_price']
            
            total_value = cash + position_value
            
            portfolio_history.append({
                'date': current_date,
                'total_value': total_value,
                'cash': cash,
                'position_value': position_value,
                'num_positions': len(positions),
                'cash_ratio': cash / total_value * 100
            })
        
        # 转换为DataFrame
        self.trades_df = pd.DataFrame(trades)
        self.portfolio_df = pd.DataFrame(portfolio_history)
        
        return self.trades_df, self.portfolio_df
    
    def calculate_performance_metrics(self, include_benchmark: bool = True):
        """计算绩效指标，包含基准比较"""
        if self.portfolio_df.empty:
            return {}
        
        # 策略绩效
        portfolio_values = self.portfolio_df['total_value'].values
        returns = np.diff(portfolio_values) / portfolio_values[:-1]
        
        # 基本指标
        total_return = (portfolio_values[-1] / self.initial_capital - 1) * 100
        annualized_return = ((portfolio_values[-1] / self.initial_capital) ** (252 / len(portfolio_values)) - 1) * 100
        
        # 风险指标
        volatility = np.std(returns) * np.sqrt(252) * 100
        sharpe_ratio = (annualized_return - 3) / volatility if volatility > 0 else 0  # 假设无风险利率3%
        
        # 最大回撤
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (portfolio_values - peak) / peak
        max_drawdown = np.min(drawdown) * 100
        
        # 交易统计
        if not self.trades_df.empty:
            sell_trades = self.trades_df[self.trades_df['action'] == 'SELL']
            
            total_trades = len(sell_trades)
            winning_trades = len(sell_trades[sell_trades['pnl'] > 0])
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
            
            avg_return_per_trade = sell_trades['pnl_pct'].mean() if not sell_trades.empty else 0
            avg_holding_days = sell_trades['holding_days'].mean() if not sell_trades.empty else 0
        else:
            total_trades = 0
            win_rate = 0
            avg_return_per_trade = 0
            avg_holding_days = 0
        
        metrics = {
            'strategy_total_return_pct': total_return,
            'strategy_annualized_return_pct': annualized_return,
            'strategy_volatility_pct': volatility,
            'strategy_sharpe_ratio': sharpe_ratio,
            'strategy_max_drawdown_pct': max_drawdown,
            'total_trades': total_trades,
            'win_rate_pct': win_rate,
            'avg_return_per_trade_pct': avg_return_per_trade,
            'avg_holding_days': avg_holding_days,
            'final_value': portfolio_values[-1]
        }
        
        # 基准比较
        if include_benchmark and hasattr(self, 'benchmark_df') and not self.benchmark_df.empty:
            # 对齐日期
            portfolio_dates = self.portfolio_df['date']
            benchmark_aligned = self.benchmark_df[self.benchmark_df['date'].isin(portfolio_dates)]
            
            if not benchmark_aligned.empty:
                benchmark_values = benchmark_aligned['benchmark_value'].values
                benchmark_returns = np.diff(benchmark_values) / benchmark_values[:-1]
                
                benchmark_total_return = (benchmark_values[-1] / self.initial_capital - 1) * 100
                benchmark_annualized_return = ((benchmark_values[-1] / self.initial_capital) ** (252 / len(benchmark_values)) - 1) * 100
                benchmark_volatility = np.std(benchmark_returns) * np.sqrt(252) * 100
                benchmark_sharpe = (benchmark_annualized_return - 3) / benchmark_volatility if benchmark_volatility > 0 else 0
                
                # 基准最大回撤
                benchmark_peak = np.maximum.accumulate(benchmark_values)
                benchmark_drawdown = (benchmark_values - benchmark_peak) / benchmark_peak
                benchmark_max_drawdown = np.min(benchmark_drawdown) * 100
                
                # 超额收益
                excess_return = total_return - benchmark_total_return
                excess_annualized = annualized_return - benchmark_annualized_return
                
                # 信息比率
                if len(returns) == len(benchmark_returns):
                    excess_returns = returns - benchmark_returns
                    tracking_error = np.std(excess_returns) * np.sqrt(252) * 100
                    information_ratio = excess_annualized / tracking_error if tracking_error > 0 else 0
                else:
                    tracking_error = 0
                    information_ratio = 0
                
                metrics.update({
                    'benchmark_total_return_pct': benchmark_total_return,
                    'benchmark_annualized_return_pct': benchmark_annualized_return,
                    'benchmark_volatility_pct': benchmark_volatility,
                    'benchmark_sharpe_ratio': benchmark_sharpe,
                    'benchmark_max_drawdown_pct': benchmark_max_drawdown,
                    'excess_return_pct': excess_return,
                    'excess_annualized_pct': excess_annualized,
                    'tracking_error_pct': tracking_error,
                    'information_ratio': information_ratio
                })
        
        return metrics

    def plot_enhanced_performance(self, output_dir: str, metrics: dict):
        """绘制增强版绩效图表，包含基准比较"""
        if self.portfolio_df.empty:
            print("No portfolio data to plot")
            return

        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        fig.suptitle('HSGT Strategy vs Benchmark Performance Analysis', fontsize=16, fontweight='bold')

        # 1. 策略 vs 基准累计收益
        ax1 = axes[0, 0]
        ax1.plot(self.portfolio_df['date'], self.portfolio_df['total_value'],
                linewidth=2, label='HSGT Strategy', color='blue')

        if hasattr(self, 'benchmark_df') and not self.benchmark_df.empty:
            # 对齐日期
            portfolio_dates = self.portfolio_df['date']
            benchmark_aligned = self.benchmark_df[self.benchmark_df['date'].isin(portfolio_dates)]
            if not benchmark_aligned.empty:
                ax1.plot(benchmark_aligned['date'], benchmark_aligned['benchmark_value'],
                        linewidth=2, label='Equal-Weight Benchmark', color='red', alpha=0.7)

        ax1.axhline(y=self.initial_capital, color='gray', linestyle='--', alpha=0.5, label='Initial Capital')
        ax1.set_title('Cumulative Performance Comparison')
        ax1.set_ylabel('Portfolio Value')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)

        # 2. 回撤对比
        ax2 = axes[0, 1]
        portfolio_values = self.portfolio_df['total_value'].values
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (portfolio_values - peak) / peak * 100
        ax2.fill_between(self.portfolio_df['date'], drawdown, 0, alpha=0.3, color='red', label='Strategy Drawdown')

        if hasattr(self, 'benchmark_df') and not self.benchmark_df.empty:
            benchmark_aligned = self.benchmark_df[self.benchmark_df['date'].isin(self.portfolio_df['date'])]
            if not benchmark_aligned.empty:
                benchmark_values = benchmark_aligned['benchmark_value'].values
                benchmark_peak = np.maximum.accumulate(benchmark_values)
                benchmark_drawdown = (benchmark_values - benchmark_peak) / benchmark_peak * 100
                ax2.fill_between(benchmark_aligned['date'], benchmark_drawdown, 0,
                               alpha=0.3, color='blue', label='Benchmark Drawdown')

        ax2.set_title('Drawdown Comparison')
        ax2.set_ylabel('Drawdown (%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='x', rotation=45)

        # 3. 现金使用率
        ax3 = axes[0, 2]
        ax3.plot(self.portfolio_df['date'], self.portfolio_df['cash_ratio'],
                linewidth=2, color='green')
        ax3.set_title('Cash Utilization')
        ax3.set_ylabel('Cash Ratio (%)')
        ax3.grid(True, alpha=0.3)
        ax3.tick_params(axis='x', rotation=45)

        # 4. 持仓数量变化
        ax4 = axes[1, 0]
        ax4.plot(self.portfolio_df['date'], self.portfolio_df['num_positions'],
                linewidth=2, color='orange')
        ax4.set_title('Number of Positions')
        ax4.set_ylabel('Position Count')
        ax4.grid(True, alpha=0.3)
        ax4.tick_params(axis='x', rotation=45)

        # 5. 交易收益分布
        ax5 = axes[1, 1]
        if not self.trades_df.empty:
            sell_trades = self.trades_df[self.trades_df['action'] == 'SELL']
            if not sell_trades.empty:
                ax5.hist(sell_trades['pnl_pct'], bins=30, alpha=0.7, edgecolor='black', color='skyblue')
                ax5.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='Break-even')
                ax5.axvline(x=sell_trades['pnl_pct'].mean(), color='green', linestyle='-',
                           alpha=0.7, label=f'Mean: {sell_trades["pnl_pct"].mean():.2f}%')
                ax5.set_title('Trade Return Distribution')
                ax5.set_xlabel('Return per Trade (%)')
                ax5.set_ylabel('Frequency')
                ax5.legend()
                ax5.grid(True, alpha=0.3)

        # 6. 绩效指标对比
        ax6 = axes[1, 2]
        if 'benchmark_total_return_pct' in metrics:
            categories = ['Total Return', 'Ann. Return', 'Volatility', 'Sharpe', 'Max DD']
            strategy_values = [
                metrics['strategy_total_return_pct'],
                metrics['strategy_annualized_return_pct'],
                metrics['strategy_volatility_pct'],
                metrics['strategy_sharpe_ratio'],
                abs(metrics['strategy_max_drawdown_pct'])
            ]
            benchmark_values = [
                metrics['benchmark_total_return_pct'],
                metrics['benchmark_annualized_return_pct'],
                metrics['benchmark_volatility_pct'],
                metrics['benchmark_sharpe_ratio'],
                abs(metrics['benchmark_max_drawdown_pct'])
            ]

            x = np.arange(len(categories))
            width = 0.35

            ax6.bar(x - width/2, strategy_values, width, label='Strategy', alpha=0.8, color='blue')
            ax6.bar(x + width/2, benchmark_values, width, label='Benchmark', alpha=0.8, color='red')

            ax6.set_title('Performance Metrics Comparison')
            ax6.set_ylabel('Value')
            ax6.set_xticks(x)
            ax6.set_xticklabels(categories, rotation=45)
            ax6.legend()
            ax6.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图片
        filename = os.path.join(output_dir, 'enhanced_strategy_performance.png')
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 Enhanced performance chart saved: {filename}")
        plt.show()

def main():
    """主函数"""
    print("="*80)
    print("    HSGT Enhanced Strategy Backtest with Benchmark")
    print("="*80)
    
    # 设置路径
    script_dir = os.path.dirname(__file__)
    analysis_dir = os.path.join(script_dir, 'analysis_results')
    output_dir = os.path.join(script_dir, 'enhanced_backtest_results')
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载交易信号数据
    signal_files = [f for f in os.listdir(analysis_dir) if f.startswith('hsgt_trading_signals_')]
    if not signal_files:
        print("Please run hsgt_predictive_analysis.py first to generate trading signals")
        return
    
    latest_signal_file = max(signal_files)
    signal_file_path = os.path.join(analysis_dir, latest_signal_file)
    
    # 创建回测器
    backtester = HSGTEnhancedStrategyBacktester(initial_capital=1000000)
    
    # 打印策略详情
    backtester.print_strategy_details()
    
    # 加载数据
    if not backtester.load_signal_data(signal_file_path):
        return
    
    # 创建基准
    backtester.create_benchmark_data()
    
    # 执行回测
    trades_df, portfolio_df = backtester.enhanced_strategy_backtest(
        holding_days=5, 
        max_positions=10
    )
    
    # 计算绩效指标
    metrics = backtester.calculate_performance_metrics(include_benchmark=True)

    # 打印详细报告
    print("\n" + "="*80)
    print("                Enhanced Strategy Performance Report")
    print("="*80)

    print(f"📊 Strategy Performance:")
    print(f"   Total Return: {metrics['strategy_total_return_pct']:.2f}%")
    print(f"   Annualized Return: {metrics['strategy_annualized_return_pct']:.2f}%")
    print(f"   Volatility: {metrics['strategy_volatility_pct']:.2f}%")
    print(f"   Sharpe Ratio: {metrics['strategy_sharpe_ratio']:.2f}")
    print(f"   Max Drawdown: {metrics['strategy_max_drawdown_pct']:.2f}%")

    if 'benchmark_total_return_pct' in metrics:
        print(f"\n📈 Benchmark Performance (Equal-Weight HK Stocks):")
        print(f"   Total Return: {metrics['benchmark_total_return_pct']:.2f}%")
        print(f"   Annualized Return: {metrics['benchmark_annualized_return_pct']:.2f}%")
        print(f"   Volatility: {metrics['benchmark_volatility_pct']:.2f}%")
        print(f"   Sharpe Ratio: {metrics['benchmark_sharpe_ratio']:.2f}")
        print(f"   Max Drawdown: {metrics['benchmark_max_drawdown_pct']:.2f}%")

        print(f"\n🎯 Relative Performance:")
        print(f"   Excess Return: {metrics['excess_return_pct']:.2f}%")
        print(f"   Excess Annualized: {metrics['excess_annualized_pct']:.2f}%")
        print(f"   Tracking Error: {metrics['tracking_error_pct']:.2f}%")
        print(f"   Information Ratio: {metrics['information_ratio']:.2f}")

    print(f"\n🔄 Trading Statistics:")
    print(f"   Total Trades: {metrics['total_trades']}")
    print(f"   Win Rate: {metrics['win_rate_pct']:.2f}%")
    print(f"   Avg Return per Trade: {metrics['avg_return_per_trade_pct']:.2f}%")
    print(f"   Avg Holding Days: {metrics['avg_holding_days']:.1f}")

    # 生成图表
    backtester.plot_enhanced_performance(output_dir, metrics)

    # 保存结果
    current_date = datetime.now().strftime('%Y%m%d_%H%M%S')

    if not trades_df.empty:
        trades_path = os.path.join(output_dir, f'enhanced_backtest_trades_{current_date}.csv')
        trades_df.to_csv(trades_path, index=False, encoding='utf-8-sig')
        print(f"💾 Enhanced trading records saved: {trades_path}")

    portfolio_path = os.path.join(output_dir, f'enhanced_backtest_portfolio_{current_date}.csv')
    portfolio_df.to_csv(portfolio_path, index=False, encoding='utf-8-sig')
    print(f"💾 Enhanced portfolio history saved: {portfolio_path}")

    # 保存绩效指标
    metrics_df = pd.DataFrame([metrics])
    metrics_path = os.path.join(output_dir, f'enhanced_backtest_metrics_{current_date}.csv')
    metrics_df.to_csv(metrics_path, index=False, encoding='utf-8-sig')
    print(f"💾 Enhanced performance metrics saved: {metrics_path}")

    # 保存基准数据
    if hasattr(backtester, 'benchmark_df') and not backtester.benchmark_df.empty:
        benchmark_path = os.path.join(output_dir, f'benchmark_data_{current_date}.csv')
        backtester.benchmark_df.to_csv(benchmark_path, index=False, encoding='utf-8-sig')
        print(f"💾 Benchmark data saved: {benchmark_path}")

    print("="*80)

if __name__ == "__main__":
    main()
