#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通持股比例变化对股价预测性分析

分析港股通持股比例变化对后续N天股价变化的先行指导作用
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from scipy import stats
from sklearn.metrics import mean_squared_error, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入交易日历工具
from utilities.utils import get_futu_trading_calendar

class HSGTPredictiveAnalyzer:
    """港股通持股比例预测性分析器"""
    
    def __init__(self, data_path: str):
        """
        初始化分析器
        
        Args:
            data_path: 数据文件路径
        """
        self.data_path = data_path
        self.df = None
        self.analysis_results = {}
        
    def load_data(self):
        """加载数据"""
        print(f"正在加载数据: {self.data_path}")
        try:
            self.df = pd.read_parquet(self.data_path)
            self.df['time_key'] = pd.to_datetime(self.df['time_key'])
            print(f"数据加载成功，共 {len(self.df)} 条记录")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def prepare_analysis_data(self, min_data_points: int = 30):
        """
        准备分析数据
        
        Args:
            min_data_points: 每只股票最少需要的数据点数
        """
        print("正在准备分析数据...")
        
        # 过滤有港股通数据的记录
        hsgt_data = self.df[self.df['hsgt_holding_ratio'].notna()].copy()
        
        if hsgt_data.empty:
            print("未找到港股通持股数据")
            return pd.DataFrame()
        
        # 按股票分组，只保留数据点足够的股票
        valid_stocks = []
        for code in hsgt_data['code'].unique():
            stock_data = hsgt_data[hsgt_data['code'] == code]
            if len(stock_data) >= min_data_points:
                valid_stocks.append(code)
        
        print(f"找到 {len(valid_stocks)} 只股票有足够的港股通数据（>={min_data_points}个数据点）")
        
        # 过滤数据
        analysis_data = hsgt_data[hsgt_data['code'].isin(valid_stocks)].copy()
        analysis_data = analysis_data.sort_values(['code', 'time_key'])
        
        return analysis_data
    
    def calculate_hsgt_changes(self, data: pd.DataFrame, lookback_days: int = 5):
        """
        计算港股通持股比例变化
        
        Args:
            data: 分析数据
            lookback_days: 回看天数
        """
        print(f"计算港股通持股比例变化（回看{lookback_days}天）...")
        
        results = []
        
        for code in data['code'].unique():
            stock_data = data[data['code'] == code].copy()
            stock_data = stock_data.sort_values('time_key')
            
            # 计算持股比例变化
            stock_data['hsgt_ratio_change_1d'] = stock_data['hsgt_holding_ratio'].diff()
            stock_data[f'hsgt_ratio_change_{lookback_days}d'] = (
                stock_data['hsgt_holding_ratio'] - 
                stock_data['hsgt_holding_ratio'].shift(lookback_days)
            )
            
            # 计算持股比例变化百分比
            stock_data['hsgt_ratio_pct_change_1d'] = stock_data['hsgt_holding_ratio'].pct_change()
            stock_data[f'hsgt_ratio_pct_change_{lookback_days}d'] = (
                (stock_data['hsgt_holding_ratio'] / 
                 stock_data['hsgt_holding_ratio'].shift(lookback_days) - 1) * 100
            )
            
            results.append(stock_data)
        
        return pd.concat(results, ignore_index=True)
    
    def calculate_future_returns(self, data: pd.DataFrame, forward_days_list: list = [1, 3, 5, 10, 20]):
        """
        计算未来收益率
        
        Args:
            data: 包含港股通变化的数据
            forward_days_list: 前瞻天数列表
        """
        print(f"计算未来收益率（前瞻天数: {forward_days_list}）...")
        
        results = []
        
        for code in data['code'].unique():
            stock_data = data[data['code'] == code].copy()
            stock_data = stock_data.sort_values('time_key')
            
            # 计算未来收益率
            for days in forward_days_list:
                # 价格变化
                stock_data[f'price_change_{days}d'] = (
                    stock_data['close'].shift(-days) - stock_data['close']
                )
                
                # 收益率
                stock_data[f'return_{days}d'] = (
                    (stock_data['close'].shift(-days) / stock_data['close'] - 1) * 100
                )
                
                # 对数收益率
                stock_data[f'log_return_{days}d'] = (
                    np.log(stock_data['close'].shift(-days) / stock_data['close']) * 100
                )
            
            results.append(stock_data)
        
        return pd.concat(results, ignore_index=True)
    
    def perform_correlation_analysis(self, data: pd.DataFrame, lookback_days: int = 5, 
                                   forward_days_list: list = [1, 3, 5, 10, 20]):
        """
        执行相关性分析
        
        Args:
            data: 完整的分析数据
            lookback_days: 港股通变化回看天数
            forward_days_list: 未来收益率前瞻天数
        """
        print("执行相关性分析...")
        
        # 准备分析用的列名
        hsgt_change_cols = [
            'hsgt_ratio_change_1d',
            f'hsgt_ratio_change_{lookback_days}d',
            'hsgt_ratio_pct_change_1d',
            f'hsgt_ratio_pct_change_{lookback_days}d'
        ]
        
        return_cols = []
        for days in forward_days_list:
            return_cols.extend([
                f'return_{days}d',
                f'log_return_{days}d'
            ])
        
        # 移除缺失值
        analysis_cols = hsgt_change_cols + return_cols
        clean_data = data[analysis_cols].dropna()
        
        if clean_data.empty:
            print("警告: 清理后的数据为空")
            return {}
        
        print(f"有效数据点: {len(clean_data)}")
        
        # 计算相关性矩阵
        correlation_matrix = clean_data.corr()
        
        # 提取港股通变化与未来收益的相关性
        correlations = {}
        for hsgt_col in hsgt_change_cols:
            correlations[hsgt_col] = {}
            for return_col in return_cols:
                corr_value = correlation_matrix.loc[hsgt_col, return_col]
                
                # 计算显著性检验
                if len(clean_data) > 2:
                    corr_coef, p_value = stats.pearsonr(
                        clean_data[hsgt_col], 
                        clean_data[return_col]
                    )
                    correlations[hsgt_col][return_col] = {
                        'correlation': corr_coef,
                        'p_value': p_value,
                        'significant': p_value < 0.05,
                        'sample_size': len(clean_data)
                    }
        
        return correlations, correlation_matrix, clean_data
    
    def generate_correlation_report(self, correlations: dict, forward_days_list: list):
        """生成相关性分析报告"""
        print("\n" + "="*80)
        print("           港股通持股比例变化与未来收益相关性分析报告")
        print("="*80)
        
        # 创建汇总表
        summary_data = []
        
        for hsgt_col, return_correlations in correlations.items():
            for return_col, stats_data in return_correlations.items():
                # 解析天数
                if 'return_' in return_col:
                    days = return_col.split('_')[1].replace('d', '')
                    return_type = 'return' if 'log_return' not in return_col else 'log_return'
                else:
                    continue
                
                summary_data.append({
                    'HSGT指标': hsgt_col,
                    '未来收益类型': return_type,
                    '前瞻天数': int(days),
                    '相关系数': stats_data['correlation'],
                    'P值': stats_data['p_value'],
                    '显著性': '是' if stats_data['significant'] else '否',
                    '样本数量': stats_data['sample_size']
                })
        
        summary_df = pd.DataFrame(summary_data)
        
        # 按相关系数绝对值排序
        summary_df['相关系数绝对值'] = summary_df['相关系数'].abs()
        summary_df = summary_df.sort_values('相关系数绝对值', ascending=False)
        
        # 显示最强相关性（前20个）
        print("\n🔥 最强相关性（前20个）:")
        top_correlations = summary_df.head(20)
        
        for _, row in top_correlations.iterrows():
            significance = "***" if row['P值'] < 0.001 else "**" if row['P值'] < 0.01 else "*" if row['P值'] < 0.05 else ""
            print(f"   {row['HSGT指标']} -> {row['未来收益类型']}_{row['前瞻天数']}天: "
                  f"相关系数={row['相关系数']:.4f} (p={row['P值']:.4f}){significance}")
        
        # 按前瞻天数分组分析
        print(f"\n📊 按前瞻天数分组的平均相关性:")
        for days in forward_days_list:
            day_data = summary_df[summary_df['前瞻天数'] == days]
            if not day_data.empty:
                avg_corr = day_data['相关系数绝对值'].mean()
                significant_count = day_data['显著性'].value_counts().get('是', 0)
                total_count = len(day_data)
                print(f"   {days}天: 平均相关系数绝对值={avg_corr:.4f}, "
                      f"显著相关数量={significant_count}/{total_count}")
        
        return summary_df

    def create_strategy_signals(self, data: pd.DataFrame, lookback_days: int = 5):
        """
        创建基于港股通变化的交易信号

        Args:
            data: 包含港股通变化的数据
            lookback_days: 回看天数
        """
        print("创建交易信号...")

        results = []

        for code in data['code'].unique():
            stock_data = data[data['code'] == code].copy()
            stock_data = stock_data.sort_values('time_key')

            if len(stock_data) < lookback_days + 10:  # 确保有足够数据
                continue

            # 计算港股通变化的分位数阈值
            hsgt_change_col = f'hsgt_ratio_change_{lookback_days}d'
            if hsgt_change_col not in stock_data.columns:
                continue

            # 移除缺失值
            valid_data = stock_data[stock_data[hsgt_change_col].notna()].copy()
            if len(valid_data) < 20:
                continue

            # 计算阈值（使用历史数据的分位数）
            threshold_high = valid_data[hsgt_change_col].quantile(0.8)  # 前20%
            threshold_low = valid_data[hsgt_change_col].quantile(0.2)   # 后20%

            # 生成信号
            valid_data['signal'] = 0  # 0: 无信号, 1: 买入, -1: 卖出
            valid_data.loc[valid_data[hsgt_change_col] >= threshold_high, 'signal'] = 1
            valid_data.loc[valid_data[hsgt_change_col] <= threshold_low, 'signal'] = -1

            results.append(valid_data)

        if results:
            return pd.concat(results, ignore_index=True)
        else:
            return pd.DataFrame()

def main():
    """主函数"""
    print("="*80)
    print("           港股通持股比例变化预测性分析")
    print("="*80)

    # 数据文件路径
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    data_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')

    if not os.path.exists(data_path):
        print(f"错误: 未找到数据文件 {data_path}")
        return

    # 创建分析器
    analyzer = HSGTPredictiveAnalyzer(data_path)

    # 加载数据
    if not analyzer.load_data():
        return

    # 准备分析数据
    analysis_data = analyzer.prepare_analysis_data(min_data_points=50)
    if analysis_data.empty:
        return

    # 分析参数
    lookback_days = 5  # 港股通变化回看天数
    forward_days_list = [1, 3, 5, 10, 20]  # 未来收益前瞻天数

    # 计算港股通变化
    data_with_hsgt_changes = analyzer.calculate_hsgt_changes(analysis_data, lookback_days)

    # 计算未来收益
    complete_data = analyzer.calculate_future_returns(data_with_hsgt_changes, forward_days_list)

    # 执行相关性分析
    correlations, correlation_matrix, clean_data = analyzer.perform_correlation_analysis(
        complete_data, lookback_days, forward_days_list
    )

    # 生成报告
    summary_df = analyzer.generate_correlation_report(correlations, forward_days_list)

    # 创建交易信号
    signal_data = analyzer.create_strategy_signals(complete_data, lookback_days)

    # 保存结果
    output_dir = os.path.join(os.path.dirname(__file__), 'analysis_results')
    os.makedirs(output_dir, exist_ok=True)

    current_date = datetime.now().strftime('%Y%m%d_%H%M%S')

    # 保存相关性汇总
    summary_path = os.path.join(output_dir, f'hsgt_correlation_summary_{current_date}.csv')
    summary_df.to_csv(summary_path, index=False, encoding='utf-8-sig')
    print(f"\n💾 相关性分析汇总已保存到: {summary_path}")

    # 保存完整数据
    complete_data_path = os.path.join(output_dir, f'hsgt_complete_analysis_data_{current_date}.csv')
    complete_data.to_csv(complete_data_path, index=False, encoding='utf-8-sig')
    print(f"💾 完整分析数据已保存到: {complete_data_path}")

    # 保存交易信号数据
    if not signal_data.empty:
        signal_path = os.path.join(output_dir, f'hsgt_trading_signals_{current_date}.csv')
        signal_data.to_csv(signal_path, index=False, encoding='utf-8-sig')
        print(f"💾 交易信号数据已保存到: {signal_path}")

    print("="*80)

if __name__ == "__main__":
    main()
