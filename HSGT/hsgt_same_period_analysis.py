#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通N天变化 vs N天收益专项分析

专门分析相同时间窗口的港股通变化与收益的关系
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置图表样式，使用英文避免乱码
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

class HSGTSamePeriodAnalyzer:
    """港股通相同时间窗口分析器"""
    
    def __init__(self, output_dir: str):
        """
        初始化分析器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def plot_same_period_correlation(self, same_period_df: pd.DataFrame):
        """绘制相同时间窗口相关性分析"""
        if same_period_df.empty:
            print("No same period data to plot")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Same Period Analysis: N-Day HSGT Change vs N-Day Returns', fontsize=16, fontweight='bold')
        
        # 1. 按天数分组的相关性
        ax1 = axes[0, 0]
        days_corr = same_period_df.groupby('days')['correlation'].mean().abs()
        bars1 = ax1.bar(days_corr.index, days_corr.values, alpha=0.7, color='skyblue')
        ax1.set_title('Average Absolute Correlation by Time Window')
        ax1.set_xlabel('Time Window (Days)')
        ax1.set_ylabel('Average Absolute Correlation')
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.0005,
                    f'{height:.4f}', ha='center', va='bottom', fontsize=9)
        
        # 2. 绝对变化 vs 百分比变化
        ax2 = axes[0, 1]
        hsgt_type_corr = same_period_df.groupby('hsgt_type')['correlation'].apply(lambda x: x.abs().mean())
        bars2 = ax2.bar(hsgt_type_corr.index, hsgt_type_corr.values, alpha=0.7, color='lightcoral')
        ax2.set_title('Absolute vs Percentage Change Correlation')
        ax2.set_xlabel('HSGT Change Type')
        ax2.set_ylabel('Average Absolute Correlation')
        ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar in bars2:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.0005,
                    f'{height:.4f}', ha='center', va='bottom', fontsize=9)
        
        # 3. 显著性分析
        ax3 = axes[1, 0]
        significance_data = same_period_df.groupby('days')['significant'].apply(lambda x: (x == True).sum() / len(x) * 100)
        bars3 = ax3.bar(significance_data.index, significance_data.values, alpha=0.7, color='lightgreen')
        ax3.set_title('Significance Ratio by Time Window')
        ax3.set_xlabel('Time Window (Days)')
        ax3.set_ylabel('Significant Correlation Ratio (%)')
        ax3.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar in bars3:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 2,
                    f'{height:.1f}%', ha='center', va='bottom', fontsize=9)
        
        # 4. 相关系数分布
        ax4 = axes[1, 1]
        correlation_values = same_period_df['correlation'].values
        ax4.hist(correlation_values, bins=15, alpha=0.7, edgecolor='black', color='orange')
        ax4.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='Zero Correlation')
        ax4.axvline(x=correlation_values.mean(), color='blue', linestyle='-', alpha=0.7, 
                   label=f'Mean: {correlation_values.mean():.4f}')
        ax4.set_title('Correlation Coefficient Distribution')
        ax4.set_xlabel('Correlation Coefficient')
        ax4.set_ylabel('Frequency')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        filename = os.path.join(self.output_dir, 'same_period_correlation_analysis.png')
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 Same period correlation analysis saved: {filename}")
        plt.show()
    
    def plot_correlation_by_timeframe_detailed(self, same_period_df: pd.DataFrame):
        """详细的时间框架相关性分析"""
        if same_period_df.empty:
            print("No same period data to plot")
            return
        
        fig, ax = plt.subplots(1, 1, figsize=(14, 8))
        
        # 准备数据
        pivot_data = same_period_df.pivot_table(
            index='days', 
            columns='hsgt_type', 
            values='correlation', 
            aggfunc='mean'
        )
        
        # 创建分组柱状图
        x = np.arange(len(pivot_data.index))
        width = 0.35
        
        if 'Absolute Change' in pivot_data.columns:
            bars1 = ax.bar(x - width/2, pivot_data['Absolute Change'], width, 
                          label='Absolute Change', alpha=0.8, color='skyblue')
        
        if 'Percentage Change' in pivot_data.columns:
            bars2 = ax.bar(x + width/2, pivot_data['Percentage Change'], width,
                          label='Percentage Change', alpha=0.8, color='lightcoral')
        
        # 设置标签和标题
        ax.set_title('N-Day HSGT Change vs N-Day Returns Correlation by Time Window', 
                     fontsize=14, fontweight='bold')
        ax.set_xlabel('Time Window (Days)')
        ax.set_ylabel('Correlation Coefficient')
        ax.set_xticks(x)
        ax.set_xticklabels(pivot_data.index)
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # 添加数值标签
        def add_value_labels(bars):
            for bar in bars:
                height = bar.get_height()
                if not np.isnan(height):
                    ax.text(bar.get_x() + bar.get_width()/2., height + 0.0005 if height >= 0 else height - 0.0015,
                           f'{height:.4f}', ha='center', va='bottom' if height >= 0 else 'top', fontsize=9)
        
        if 'Absolute Change' in pivot_data.columns:
            add_value_labels(bars1)
        if 'Percentage Change' in pivot_data.columns:
            add_value_labels(bars2)
        
        plt.tight_layout()
        
        # 保存图片
        filename = os.path.join(self.output_dir, 'detailed_timeframe_correlation.png')
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 Detailed timeframe correlation saved: {filename}")
        plt.show()
    
    def create_correlation_summary_table(self, same_period_df: pd.DataFrame):
        """创建相关性汇总表"""
        if same_period_df.empty:
            print("No same period data to summarize")
            return
        
        # 创建汇总表
        summary_table = same_period_df.pivot_table(
            index='days',
            columns='hsgt_type',
            values=['correlation', 'p_value', 'significant'],
            aggfunc={'correlation': 'mean', 'p_value': 'mean', 'significant': lambda x: (x == True).sum()}
        )
        
        print("\n" + "="*80)
        print("    Same Period Correlation Summary Table")
        print("="*80)
        
        for days in sorted(same_period_df['days'].unique()):
            day_data = same_period_df[same_period_df['days'] == days]
            print(f"\n📊 {days}-Day Analysis:")
            
            for _, row in day_data.iterrows():
                significance = "***" if row['p_value'] < 0.001 else "**" if row['p_value'] < 0.01 else "*" if row['p_value'] < 0.05 else ""
                print(f"   {row['hsgt_type']:18} vs {days}D Return: "
                      f"r={row['correlation']:7.4f} (p={row['p_value']:.4f}){significance}")
        
        # 计算最佳时间窗口
        best_correlation = same_period_df.loc[same_period_df['correlation'].abs().idxmax()]
        print(f"\n🎯 Strongest Same-Period Correlation:")
        print(f"   {best_correlation['days']}D {best_correlation['hsgt_type']} vs {best_correlation['days']}D Return")
        print(f"   Correlation: {best_correlation['correlation']:.4f}")
        print(f"   P-value: {best_correlation['p_value']:.4f}")
        print(f"   Significant: {'Yes' if best_correlation['significant'] else 'No'}")

def load_same_period_data(analysis_dir: str):
    """加载相同时间窗口分析数据"""
    if not os.path.exists(analysis_dir):
        print(f"Analysis directory not found: {analysis_dir}")
        return None
    
    # 查找最新的相同时间窗口分析文件
    same_period_files = [f for f in os.listdir(analysis_dir) if f.startswith('hsgt_same_period_analysis_')]
    
    if not same_period_files:
        print("No same period analysis files found")
        return None
    
    latest_file = max(same_period_files)
    file_path = os.path.join(analysis_dir, latest_file)
    
    print(f"Loading same period analysis: {latest_file}")
    
    try:
        same_period_df = pd.read_csv(file_path)
        return same_period_df
    except Exception as e:
        print(f"Failed to load same period analysis: {e}")
        return None

def main():
    """主函数"""
    print("="*80)
    print("    HSGT Same Period Analysis (N-Day Change vs N-Day Returns)")
    print("="*80)
    
    # 设置路径
    script_dir = os.path.dirname(__file__)
    analysis_dir = os.path.join(script_dir, 'analysis_results')
    output_dir = os.path.join(script_dir, 'same_period_results')
    
    # 加载相同时间窗口分析结果
    same_period_df = load_same_period_data(analysis_dir)
    
    if same_period_df is None:
        print("Please run hsgt_predictive_analysis.py first to generate same period analysis data")
        return
    
    # 创建分析器
    analyzer = HSGTSamePeriodAnalyzer(output_dir)
    
    # 生成可视化
    print("\nGenerating same period analysis visualizations...")
    
    # 1. 相同时间窗口相关性分析
    analyzer.plot_same_period_correlation(same_period_df)
    
    # 2. 详细的时间框架分析
    analyzer.plot_correlation_by_timeframe_detailed(same_period_df)
    
    # 3. 创建汇总表
    analyzer.create_correlation_summary_table(same_period_df)
    
    print(f"\n📊 All same period analysis results saved to: {output_dir}")
    print("="*80)

if __name__ == "__main__":
    main()
