#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通持股比例变化交易策略回测

基于港股通持股比例变化构建交易策略并进行回测
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

class HSGTStrategyBacktester:
    """港股通策略回测器"""
    
    def __init__(self, initial_capital: float = 1000000):
        """
        初始化回测器
        
        Args:
            initial_capital: 初始资金
        """
        self.initial_capital = initial_capital
        self.results = {}
    
    def load_signal_data(self, signal_file_path: str):
        """加载交易信号数据"""
        try:
            self.signal_data = pd.read_csv(signal_file_path)
            self.signal_data['time_key'] = pd.to_datetime(self.signal_data['time_key'])
            print(f"交易信号数据加载成功，共 {len(self.signal_data)} 条记录")
            return True
        except Exception as e:
            print(f"加载交易信号数据失败: {e}")
            return False
    
    def simple_strategy_backtest(self, holding_days: int = 5, max_positions: int = 10):
        """
        简单策略回测
        
        Args:
            holding_days: 持仓天数
            max_positions: 最大持仓数量
        """
        print(f"执行简单策略回测（持仓{holding_days}天，最大{max_positions}个仓位）...")
        
        # 准备数据
        data = self.signal_data.copy()
        data = data.sort_values(['time_key', 'code'])
        
        # 初始化回测变量
        portfolio_value = self.initial_capital
        cash = self.initial_capital
        positions = {}  # {code: {'shares': shares, 'entry_price': price, 'entry_date': date}}
        trades = []
        portfolio_history = []
        
        # 按日期进行回测
        trading_dates = sorted(data['time_key'].unique())
        
        for current_date in trading_dates:
            daily_data = data[data['time_key'] == current_date]
            
            # 检查是否需要平仓（持仓超过指定天数）
            positions_to_close = []
            for code, position in positions.items():
                days_held = (current_date - position['entry_date']).days
                if days_held >= holding_days:
                    positions_to_close.append(code)
            
            # 平仓
            for code in positions_to_close:
                position = positions[code]
                current_price_data = daily_data[daily_data['code'] == code]
                
                if not current_price_data.empty:
                    exit_price = current_price_data['close'].iloc[0]
                    shares = position['shares']
                    exit_value = shares * exit_price
                    
                    # 计算收益
                    entry_value = shares * position['entry_price']
                    pnl = exit_value - entry_value
                    pnl_pct = (exit_price / position['entry_price'] - 1) * 100
                    
                    # 记录交易
                    trades.append({
                        'code': code,
                        'action': 'SELL',
                        'date': current_date,
                        'price': exit_price,
                        'shares': shares,
                        'value': exit_value,
                        'pnl': pnl,
                        'pnl_pct': pnl_pct,
                        'holding_days': days_held
                    })
                    
                    cash += exit_value
                    del positions[code]
            
            # 寻找买入信号
            buy_signals = daily_data[daily_data['signal'] == 1]
            
            # 按港股通变化幅度排序，优先买入变化最大的
            if not buy_signals.empty and 'hsgt_ratio_change_5d' in buy_signals.columns:
                buy_signals = buy_signals.sort_values('hsgt_ratio_change_5d', ascending=False)
            
            # 执行买入
            for _, signal_row in buy_signals.iterrows():
                code = signal_row['code']
                
                # 检查是否已持仓或达到最大持仓数
                if code in positions or len(positions) >= max_positions:
                    continue
                
                # 检查是否有足够现金
                price = signal_row['close']
                position_size = cash / max_positions  # 等权重分配
                shares = int(position_size / price)
                
                if shares > 0 and shares * price <= cash:
                    # 买入
                    entry_value = shares * price
                    cash -= entry_value
                    
                    positions[code] = {
                        'shares': shares,
                        'entry_price': price,
                        'entry_date': current_date
                    }
                    
                    # 记录交易
                    trades.append({
                        'code': code,
                        'action': 'BUY',
                        'date': current_date,
                        'price': price,
                        'shares': shares,
                        'value': entry_value,
                        'pnl': 0,
                        'pnl_pct': 0,
                        'holding_days': 0
                    })
            
            # 计算当日组合价值
            position_value = 0
            for code, position in positions.items():
                current_price_data = daily_data[daily_data['code'] == code]
                if not current_price_data.empty:
                    current_price = current_price_data['close'].iloc[0]
                    position_value += position['shares'] * current_price
                else:
                    # 如果没有当日数据，使用入场价格
                    position_value += position['shares'] * position['entry_price']
            
            total_value = cash + position_value
            
            portfolio_history.append({
                'date': current_date,
                'total_value': total_value,
                'cash': cash,
                'position_value': position_value,
                'num_positions': len(positions)
            })
        
        # 转换为DataFrame
        self.trades_df = pd.DataFrame(trades)
        self.portfolio_df = pd.DataFrame(portfolio_history)
        
        return self.trades_df, self.portfolio_df
    
    def calculate_performance_metrics(self):
        """计算绩效指标"""
        if self.portfolio_df.empty:
            return {}
        
        # 计算收益率
        portfolio_values = self.portfolio_df['total_value'].values
        returns = np.diff(portfolio_values) / portfolio_values[:-1]
        
        # 基本指标
        total_return = (portfolio_values[-1] / self.initial_capital - 1) * 100
        annualized_return = ((portfolio_values[-1] / self.initial_capital) ** (252 / len(portfolio_values)) - 1) * 100
        
        # 风险指标
        volatility = np.std(returns) * np.sqrt(252) * 100
        sharpe_ratio = (annualized_return - 3) / volatility if volatility > 0 else 0  # 假设无风险利率3%
        
        # 最大回撤
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (portfolio_values - peak) / peak
        max_drawdown = np.min(drawdown) * 100
        
        # 交易统计
        if not self.trades_df.empty:
            buy_trades = self.trades_df[self.trades_df['action'] == 'BUY']
            sell_trades = self.trades_df[self.trades_df['action'] == 'SELL']
            
            total_trades = len(sell_trades)
            winning_trades = len(sell_trades[sell_trades['pnl'] > 0])
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
            
            avg_return_per_trade = sell_trades['pnl_pct'].mean() if not sell_trades.empty else 0
            avg_holding_days = sell_trades['holding_days'].mean() if not sell_trades.empty else 0
        else:
            total_trades = 0
            win_rate = 0
            avg_return_per_trade = 0
            avg_holding_days = 0
        
        metrics = {
            'total_return_pct': total_return,
            'annualized_return_pct': annualized_return,
            'volatility_pct': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown,
            'total_trades': total_trades,
            'win_rate_pct': win_rate,
            'avg_return_per_trade_pct': avg_return_per_trade,
            'avg_holding_days': avg_holding_days,
            'final_value': portfolio_values[-1]
        }
        
        return metrics
    
    def print_performance_report(self, metrics: dict):
        """打印绩效报告"""
        print("\n" + "="*80)
        print("                    策略回测绩效报告")
        print("="*80)
        
        print(f"📊 收益指标:")
        print(f"   总收益率: {metrics['total_return_pct']:.2f}%")
        print(f"   年化收益率: {metrics['annualized_return_pct']:.2f}%")
        print(f"   最终资产价值: ¥{metrics['final_value']:,.2f}")
        
        print(f"\n📈 风险指标:")
        print(f"   年化波动率: {metrics['volatility_pct']:.2f}%")
        print(f"   夏普比率: {metrics['sharpe_ratio']:.2f}")
        print(f"   最大回撤: {metrics['max_drawdown_pct']:.2f}%")
        
        print(f"\n🔄 交易统计:")
        print(f"   总交易次数: {metrics['total_trades']}")
        print(f"   胜率: {metrics['win_rate_pct']:.2f}%")
        print(f"   平均每笔收益: {metrics['avg_return_per_trade_pct']:.2f}%")
        print(f"   平均持仓天数: {metrics['avg_holding_days']:.1f}天")
        
        print("="*80)
    
    def plot_performance(self, output_dir: str):
        """绘制绩效图表"""
        if self.portfolio_df.empty:
            print("没有组合数据可供绘制")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('港股通策略回测结果', fontsize=16, fontweight='bold')
        
        # 1. 组合价值曲线
        ax1 = axes[0, 0]
        ax1.plot(self.portfolio_df['date'], self.portfolio_df['total_value'], linewidth=2)
        ax1.axhline(y=self.initial_capital, color='r', linestyle='--', alpha=0.7, label='初始资金')
        ax1.set_title('组合价值变化')
        ax1.set_ylabel('组合价值 (¥)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)
        
        # 2. 现金与持仓价值
        ax2 = axes[0, 1]
        ax2.plot(self.portfolio_df['date'], self.portfolio_df['cash'], label='现金', linewidth=2)
        ax2.plot(self.portfolio_df['date'], self.portfolio_df['position_value'], label='持仓价值', linewidth=2)
        ax2.set_title('现金与持仓价值')
        ax2.set_ylabel('价值 (¥)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='x', rotation=45)
        
        # 3. 持仓数量
        ax3 = axes[1, 0]
        ax3.plot(self.portfolio_df['date'], self.portfolio_df['num_positions'], linewidth=2, color='green')
        ax3.set_title('持仓股票数量')
        ax3.set_ylabel('持仓数量')
        ax3.grid(True, alpha=0.3)
        ax3.tick_params(axis='x', rotation=45)
        
        # 4. 交易收益分布
        ax4 = axes[1, 1]
        if not self.trades_df.empty:
            sell_trades = self.trades_df[self.trades_df['action'] == 'SELL']
            if not sell_trades.empty:
                ax4.hist(sell_trades['pnl_pct'], bins=20, alpha=0.7, edgecolor='black')
                ax4.axvline(x=0, color='r', linestyle='--', alpha=0.7)
                ax4.set_title('单笔交易收益分布')
                ax4.set_xlabel('收益率 (%)')
                ax4.set_ylabel('频次')
                ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        filename = os.path.join(output_dir, 'strategy_backtest_performance.png')
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 策略回测图表已保存: {filename}")
        plt.show()

def load_latest_signal_data(analysis_dir: str):
    """加载最新的交易信号数据"""
    if not os.path.exists(analysis_dir):
        print(f"分析结果目录不存在: {analysis_dir}")
        return None
    
    signal_files = [f for f in os.listdir(analysis_dir) if f.startswith('hsgt_trading_signals_')]
    
    if not signal_files:
        print("未找到交易信号文件")
        return None
    
    latest_file = max(signal_files)
    return os.path.join(analysis_dir, latest_file)

def main():
    """主函数"""
    print("="*80)
    print("           港股通持股比例变化策略回测")
    print("="*80)
    
    # 设置路径
    script_dir = os.path.dirname(__file__)
    analysis_dir = os.path.join(script_dir, 'analysis_results')
    output_dir = os.path.join(script_dir, 'backtest_results')
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载交易信号数据
    signal_file = load_latest_signal_data(analysis_dir)
    if signal_file is None:
        print("请先运行 hsgt_predictive_analysis.py 生成交易信号")
        return
    
    # 创建回测器
    backtester = HSGTStrategyBacktester(initial_capital=1000000)
    
    # 加载数据
    if not backtester.load_signal_data(signal_file):
        return
    
    # 执行回测
    trades_df, portfolio_df = backtester.simple_strategy_backtest(
        holding_days=5, 
        max_positions=10
    )
    
    # 计算绩效指标
    metrics = backtester.calculate_performance_metrics()
    
    # 打印报告
    backtester.print_performance_report(metrics)
    
    # 生成图表
    backtester.plot_performance(output_dir)
    
    # 保存结果
    current_date = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    if not trades_df.empty:
        trades_path = os.path.join(output_dir, f'backtest_trades_{current_date}.csv')
        trades_df.to_csv(trades_path, index=False, encoding='utf-8-sig')
        print(f"💾 交易记录已保存到: {trades_path}")
    
    portfolio_path = os.path.join(output_dir, f'backtest_portfolio_{current_date}.csv')
    portfolio_df.to_csv(portfolio_path, index=False, encoding='utf-8-sig')
    print(f"💾 组合历史已保存到: {portfolio_path}")
    
    # 保存绩效指标
    metrics_df = pd.DataFrame([metrics])
    metrics_path = os.path.join(output_dir, f'backtest_metrics_{current_date}.csv')
    metrics_df.to_csv(metrics_path, index=False, encoding='utf-8-sig')
    print(f"💾 绩效指标已保存到: {metrics_path}")
    
    print("="*80)

if __name__ == "__main__":
    main()
