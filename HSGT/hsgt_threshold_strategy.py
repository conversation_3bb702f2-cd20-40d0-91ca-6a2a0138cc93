#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通阈值策略

只在港股通持股比例发生显著变化时才交易
测试不同的变化阈值对策略表现的影响
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置图表样式，使用英文避免乱码
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

class HSGTThresholdStrategy:
    """港股通阈值策略回测器"""
    
    def __init__(self, initial_capital: float = 1000000):
        """
        初始化策略
        
        Args:
            initial_capital: 初始资金
        """
        self.initial_capital = initial_capital
        self.data = None
        
    def load_data(self, data_path: str):
        """加载港股数据"""
        try:
            self.data = pd.read_parquet(data_path)
            self.data['time_key'] = pd.to_datetime(self.data['time_key'])
            print(f"Data loaded: {len(self.data)} records")
            
            # 过滤有港股通数据的记录
            self.data = self.data[self.data['hsgt_holding_ratio'].notna()].copy()
            print(f"Records with HSGT data: {len(self.data)}")
            
            return True
        except Exception as e:
            print(f"Failed to load data: {e}")
            return False
    
    def calculate_hsgt_changes_with_stats(self):
        """计算港股通变化并统计分布"""
        print("Calculating HSGT changes and statistics...")
        
        results = []
        all_changes = []
        
        for code in self.data['code'].unique():
            stock_data = self.data[self.data['code'] == code].copy()
            stock_data = stock_data.sort_values('time_key')
            
            if len(stock_data) < 2:
                continue
            
            # 计算1天港股通持股比例变化（绝对值）
            stock_data['hsgt_ratio_change_1d'] = stock_data['hsgt_holding_ratio'].diff()
            
            # 计算次日收益率
            stock_data['next_day_return'] = stock_data['close'].shift(-1) / stock_data['close'] - 1
            
            # 收集所有变化值用于统计
            valid_changes = stock_data['hsgt_ratio_change_1d'].dropna()
            all_changes.extend(valid_changes.tolist())
            
            results.append(stock_data)
        
        self.data_with_changes = pd.concat(results, ignore_index=True)
        
        # 统计港股通变化分布
        all_changes = np.array(all_changes)
        all_changes = all_changes[np.isfinite(all_changes)]  # 移除无穷大和NaN
        
        print(f"\n📊 HSGT Change Distribution Statistics:")
        print(f"   Total observations: {len(all_changes):,}")
        print(f"   Mean: {np.mean(all_changes):.4f}%")
        print(f"   Std: {np.std(all_changes):.4f}%")
        print(f"   Min: {np.min(all_changes):.4f}%")
        print(f"   Max: {np.max(all_changes):.4f}%")
        
        # 分位数统计
        percentiles = [1, 5, 10, 25, 50, 75, 90, 95, 99]
        print(f"\n   Percentiles:")
        for p in percentiles:
            value = np.percentile(all_changes, p)
            print(f"   {p:2d}%: {value:8.4f}%")
        
        # 建议的阈值
        suggested_thresholds = [
            np.percentile(all_changes, 90),  # 90分位数
            np.percentile(all_changes, 95),  # 95分位数
            np.percentile(all_changes, 99),  # 99分位数
            1.0,  # 用户建议的1个百分点
            2.0,  # 2个百分点
            3.0   # 3个百分点
        ]
        
        print(f"\n💡 Suggested thresholds to test:")
        for i, threshold in enumerate(suggested_thresholds):
            pct_above = (all_changes >= threshold).mean() * 100
            print(f"   {threshold:.2f}%: {pct_above:.2f}% of observations above this threshold")
        
        return self.data_with_changes, suggested_thresholds
    
    def threshold_strategy_backtest(self, threshold: float, max_positions: int = 1):
        """
        执行阈值策略回测
        
        Args:
            threshold: 港股通变化阈值（百分点）
            max_positions: 最大持仓数量
        """
        print(f"\nTesting threshold strategy with {threshold:.2f}% threshold...")
        
        # 准备数据
        data = self.data_with_changes.copy()
        data = data.sort_values(['time_key', 'code'])
        
        # 初始化回测变量
        portfolio_value = self.initial_capital
        cash = self.initial_capital
        positions = {}  # {code: {'shares': shares, 'entry_price': price, 'entry_date': date}}
        trades = []
        portfolio_history = []
        
        # 按日期进行回测
        trading_dates = sorted(data['time_key'].unique())
        
        for i, current_date in enumerate(trading_dates[:-1]):
            current_data = data[data['time_key'] == current_date]
            
            # 平仓（持有1天后卖出）
            positions_to_close = list(positions.keys())
            for code in positions_to_close:
                position = positions[code]
                current_price_data = current_data[current_data['code'] == code]
                
                if not current_price_data.empty:
                    exit_price = current_price_data['close'].iloc[0]
                    shares = position['shares']
                    exit_value = shares * exit_price
                    
                    # 计算收益
                    entry_value = shares * position['entry_price']
                    pnl = exit_value - entry_value
                    pnl_pct = (exit_price / position['entry_price'] - 1) * 100
                    
                    # 记录交易
                    trades.append({
                        'entry_date': position['entry_date'],
                        'exit_date': current_date,
                        'code': code,
                        'action': 'SELL',
                        'entry_price': position['entry_price'],
                        'exit_price': exit_price,
                        'shares': shares,
                        'entry_value': entry_value,
                        'exit_value': exit_value,
                        'pnl': pnl,
                        'pnl_pct': pnl_pct,
                        'hsgt_change': position.get('hsgt_change', 0)
                    })
                    
                    cash += exit_value
                    del positions[code]
            
            # 寻找符合阈值条件的买入机会
            valid_data = current_data[
                (current_data['hsgt_ratio_change_1d'].notna()) &
                (current_data['next_day_return'].notna()) &
                (current_data['hsgt_ratio_change_1d'] >= threshold)  # 阈值条件
            ].copy()
            
            if not valid_data.empty and len(positions) < max_positions:
                # 按港股通变化幅度排序，选择变化最大的
                valid_data = valid_data.sort_values('hsgt_ratio_change_1d', ascending=False)
                
                # 买入前N只股票
                stocks_to_buy = valid_data.head(max_positions - len(positions))
                
                for _, stock in stocks_to_buy.iterrows():
                    code = stock['code']
                    
                    if code not in positions:
                        price = stock['close']
                        position_size = cash / max_positions if max_positions > 1 else cash
                        shares = position_size / price
                        
                        if shares * price <= cash:
                            # 买入
                            entry_value = shares * price
                            cash -= entry_value
                            
                            positions[code] = {
                                'shares': shares,
                                'entry_price': price,
                                'entry_date': current_date,
                                'hsgt_change': stock['hsgt_ratio_change_1d']
                            }
                            
                            # 记录买入交易
                            trades.append({
                                'entry_date': current_date,
                                'exit_date': None,
                                'code': code,
                                'action': 'BUY',
                                'entry_price': price,
                                'exit_price': None,
                                'shares': shares,
                                'entry_value': entry_value,
                                'exit_value': None,
                                'pnl': 0,
                                'pnl_pct': 0,
                                'hsgt_change': stock['hsgt_ratio_change_1d']
                            })
            
            # 计算当日组合价值
            position_value = 0
            for code, position in positions.items():
                current_price_data = current_data[current_data['code'] == code]
                if not current_price_data.empty:
                    current_price = current_price_data['close'].iloc[0]
                    position_value += position['shares'] * current_price
                else:
                    position_value += position['shares'] * position['entry_price']
            
            total_value = cash + position_value
            
            portfolio_history.append({
                'date': current_date,
                'portfolio_value': total_value,
                'cash': cash,
                'position_value': position_value,
                'num_positions': len(positions),
                'cash_ratio': cash / total_value * 100 if total_value > 0 else 100
            })
        
        # 转换为DataFrame
        trades_df = pd.DataFrame(trades)
        portfolio_df = pd.DataFrame(portfolio_history)
        
        return trades_df, portfolio_df
    
    def calculate_performance_metrics(self, portfolio_df: pd.DataFrame, trades_df: pd.DataFrame):
        """计算绩效指标"""
        if portfolio_df.empty:
            return {}
        
        portfolio_values = portfolio_df['portfolio_value'].values
        
        # 计算日收益率
        daily_returns = np.diff(portfolio_values) / portfolio_values[:-1]
        
        # 基本指标
        total_return = (portfolio_values[-1] / self.initial_capital - 1) * 100
        
        # 年化收益率
        trading_days = len(portfolio_values)
        if trading_days > 1:
            annualized_return = ((portfolio_values[-1] / self.initial_capital) ** (252 / trading_days) - 1) * 100
        else:
            annualized_return = 0
        
        # 风险指标
        if len(daily_returns) > 0:
            volatility = np.std(daily_returns) * np.sqrt(252) * 100
            sharpe_ratio = (annualized_return - 3) / volatility if volatility > 0 else 0
        else:
            volatility = 0
            sharpe_ratio = 0
        
        # 最大回撤
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (portfolio_values - peak) / peak
        max_drawdown = np.min(drawdown) * 100
        
        # 交易统计
        if not trades_df.empty:
            sell_trades = trades_df[trades_df['action'] == 'SELL']
            
            total_trades = len(sell_trades)
            if total_trades > 0:
                winning_trades = len(sell_trades[sell_trades['pnl'] > 0])
                win_rate = (winning_trades / total_trades * 100)
                avg_return_per_trade = sell_trades['pnl_pct'].mean()
                avg_hsgt_change = sell_trades['hsgt_change'].mean()
            else:
                win_rate = 0
                avg_return_per_trade = 0
                avg_hsgt_change = 0
        else:
            total_trades = 0
            win_rate = 0
            avg_return_per_trade = 0
            avg_hsgt_change = 0
        
        metrics = {
            'total_return_pct': total_return,
            'annualized_return_pct': annualized_return,
            'volatility_pct': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_pct': max_drawdown,
            'total_trades': total_trades,
            'win_rate_pct': win_rate,
            'avg_return_per_trade_pct': avg_return_per_trade,
            'avg_hsgt_change': avg_hsgt_change,
            'final_value': portfolio_values[-1]
        }
        
        return metrics
    
    def test_multiple_thresholds(self, thresholds: list):
        """测试多个阈值"""
        print(f"\n{'='*80}")
        print("    Testing Multiple Thresholds")
        print(f"{'='*80}")
        
        results = []
        
        for threshold in thresholds:
            trades_df, portfolio_df = self.threshold_strategy_backtest(threshold)
            metrics = self.calculate_performance_metrics(portfolio_df, trades_df)
            
            result = {
                'threshold': threshold,
                **metrics
            }
            results.append(result)
            
            print(f"Threshold {threshold:6.2f}%: "
                  f"Return {metrics['total_return_pct']:8.1f}%, "
                  f"Trades {metrics['total_trades']:4d}, "
                  f"Win Rate {metrics['win_rate_pct']:5.1f}%")
        
        return pd.DataFrame(results)
    
    def plot_threshold_analysis(self, results_df: pd.DataFrame, output_dir: str):
        """绘制阈值分析结果"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('HSGT Threshold Strategy Analysis', fontsize=16, fontweight='bold')
        
        thresholds = results_df['threshold'].values
        
        # 1. 总收益率 vs 阈值
        ax1 = axes[0, 0]
        ax1.plot(thresholds, results_df['total_return_pct'], 'bo-', linewidth=2, markersize=6)
        ax1.set_title('Total Return vs Threshold')
        ax1.set_xlabel('HSGT Change Threshold (%)')
        ax1.set_ylabel('Total Return (%)')
        ax1.grid(True, alpha=0.3)
        
        # 2. 交易次数 vs 阈值
        ax2 = axes[0, 1]
        ax2.plot(thresholds, results_df['total_trades'], 'ro-', linewidth=2, markersize=6)
        ax2.set_title('Number of Trades vs Threshold')
        ax2.set_xlabel('HSGT Change Threshold (%)')
        ax2.set_ylabel('Total Trades')
        ax2.grid(True, alpha=0.3)
        
        # 3. 夏普比率 vs 阈值
        ax3 = axes[1, 0]
        ax3.plot(thresholds, results_df['sharpe_ratio'], 'go-', linewidth=2, markersize=6)
        ax3.set_title('Sharpe Ratio vs Threshold')
        ax3.set_xlabel('HSGT Change Threshold (%)')
        ax3.set_ylabel('Sharpe Ratio')
        ax3.grid(True, alpha=0.3)
        ax3.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='Sharpe = 1.0')
        ax3.legend()
        
        # 4. 胜率 vs 阈值
        ax4 = axes[1, 1]
        ax4.plot(thresholds, results_df['win_rate_pct'], 'mo-', linewidth=2, markersize=6)
        ax4.set_title('Win Rate vs Threshold')
        ax4.set_xlabel('HSGT Change Threshold (%)')
        ax4.set_ylabel('Win Rate (%)')
        ax4.grid(True, alpha=0.3)
        ax4.axhline(y=50.0, color='red', linestyle='--', alpha=0.7, label='50% (Random)')
        ax4.legend()
        
        plt.tight_layout()
        
        # 保存图片
        filename = os.path.join(output_dir, 'threshold_analysis.png')
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 Threshold analysis chart saved: {filename}")
        plt.show()

def main():
    """主函数"""
    print("="*80)
    print("    HSGT Threshold Strategy Analysis")
    print("="*80)
    
    # 设置路径
    script_dir = os.path.dirname(__file__)
    project_root = os.path.abspath(os.path.join(script_dir, '..'))
    data_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')
    output_dir = os.path.join(script_dir, 'threshold_strategy_results')
    os.makedirs(output_dir, exist_ok=True)
    
    if not os.path.exists(data_path):
        print(f"Error: Data file not found {data_path}")
        return
    
    # 创建策略实例
    strategy = HSGTThresholdStrategy(initial_capital=1000000)
    
    # 加载数据
    if not strategy.load_data(data_path):
        return
    
    # 计算港股通变化并获取建议阈值
    data_with_changes, suggested_thresholds = strategy.calculate_hsgt_changes_with_stats()
    
    # 测试多个阈值
    results_df = strategy.test_multiple_thresholds(suggested_thresholds)
    
    # 找出最优阈值
    best_return_idx = results_df['total_return_pct'].idxmax()
    best_sharpe_idx = results_df['sharpe_ratio'].idxmax()
    
    print(f"\n{'='*80}")
    print("    Optimal Threshold Analysis")
    print(f"{'='*80}")
    
    print(f"🏆 Best Total Return:")
    best_return_row = results_df.iloc[best_return_idx]
    print(f"   Threshold: {best_return_row['threshold']:.2f}%")
    print(f"   Total Return: {best_return_row['total_return_pct']:.2f}%")
    print(f"   Trades: {best_return_row['total_trades']}")
    print(f"   Win Rate: {best_return_row['win_rate_pct']:.2f}%")
    
    print(f"\n🎯 Best Sharpe Ratio:")
    best_sharpe_row = results_df.iloc[best_sharpe_idx]
    print(f"   Threshold: {best_sharpe_row['threshold']:.2f}%")
    print(f"   Sharpe Ratio: {best_sharpe_row['sharpe_ratio']:.2f}")
    print(f"   Total Return: {best_sharpe_row['total_return_pct']:.2f}%")
    print(f"   Trades: {best_sharpe_row['total_trades']}")
    
    # 与无阈值策略对比
    no_threshold_trades, no_threshold_portfolio = strategy.threshold_strategy_backtest(0.0)
    no_threshold_metrics = strategy.calculate_performance_metrics(no_threshold_portfolio, no_threshold_trades)
    
    print(f"\n📊 Comparison with No Threshold Strategy:")
    print(f"   No Threshold: {no_threshold_metrics['total_return_pct']:.2f}% return, {no_threshold_metrics['total_trades']} trades")
    print(f"   Best Threshold: {best_return_row['total_return_pct']:.2f}% return, {best_return_row['total_trades']} trades")
    print(f"   Improvement: {best_return_row['total_return_pct'] - no_threshold_metrics['total_return_pct']:.2f}% return")
    
    # 生成图表
    strategy.plot_threshold_analysis(results_df, output_dir)
    
    # 保存结果
    current_date = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_path = os.path.join(output_dir, f'threshold_analysis_results_{current_date}.csv')
    results_df.to_csv(results_path, index=False, encoding='utf-8-sig')
    print(f"💾 Threshold analysis results saved: {results_path}")
    
    print("="*80)

if __name__ == "__main__":
    main()
