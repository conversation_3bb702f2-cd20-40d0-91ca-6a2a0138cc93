#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通持股比例变化可视化分析

生成各种图表来分析港股通持股比例变化与股价的关系
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# --- Path Setup ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

class HSGTVisualizationAnalyzer:
    """港股通可视化分析器"""
    
    def __init__(self, output_dir: str):
        """
        初始化可视化分析器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def plot_correlation_heatmap(self, correlation_matrix: pd.DataFrame, title: str = "相关性热力图"):
        """绘制相关性热力图"""
        plt.figure(figsize=(16, 12))
        
        # 只显示港股通变化与未来收益的相关性
        hsgt_cols = [col for col in correlation_matrix.columns if 'hsgt_ratio' in col]
        return_cols = [col for col in correlation_matrix.columns if 'return_' in col]
        
        if hsgt_cols and return_cols:
            subset_corr = correlation_matrix.loc[hsgt_cols, return_cols]
            
            # 创建热力图
            mask = np.zeros_like(subset_corr, dtype=bool)
            sns.heatmap(subset_corr, 
                       annot=True, 
                       cmap='RdBu_r', 
                       center=0,
                       square=True,
                       fmt='.3f',
                       cbar_kws={"shrink": .8})
            
            plt.title(title, fontsize=16, fontweight='bold')
            plt.xlabel('未来收益率', fontsize=12)
            plt.ylabel('港股通持股比例变化', fontsize=12)
            plt.xticks(rotation=45, ha='right')
            plt.yticks(rotation=0)
            plt.tight_layout()
            
            # 保存图片
            filename = os.path.join(self.output_dir, 'correlation_heatmap.png')
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"📊 相关性热力图已保存: {filename}")
            plt.show()
    
    def plot_correlation_by_timeframe(self, summary_df: pd.DataFrame):
        """按时间框架绘制相关性"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('港股通持股比例变化与未来收益相关性分析', fontsize=16, fontweight='bold')
        
        # 1. 按前瞻天数的平均相关性
        ax1 = axes[0, 0]
        timeframe_corr = summary_df.groupby('前瞻天数')['相关系数绝对值'].mean().sort_index()
        bars1 = ax1.bar(timeframe_corr.index, timeframe_corr.values, alpha=0.7, color='skyblue')
        ax1.set_title('按前瞻天数的平均相关性强度')
        ax1.set_xlabel('前瞻天数')
        ax1.set_ylabel('平均相关系数绝对值')
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                    f'{height:.3f}', ha='center', va='bottom')
        
        # 2. 显著相关性数量
        ax2 = axes[0, 1]
        significant_counts = summary_df.groupby('前瞻天数')['显著性'].apply(lambda x: (x == '是').sum())
        total_counts = summary_df.groupby('前瞻天数').size()
        significant_ratio = (significant_counts / total_counts * 100).sort_index()
        
        bars2 = ax2.bar(significant_ratio.index, significant_ratio.values, alpha=0.7, color='lightcoral')
        ax2.set_title('显著相关性比例')
        ax2.set_xlabel('前瞻天数')
        ax2.set_ylabel('显著相关性比例 (%)')
        ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar in bars2:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{height:.1f}%', ha='center', va='bottom')
        
        # 3. 不同HSGT指标的相关性分布
        ax3 = axes[1, 0]
        hsgt_indicators = summary_df['HSGT指标'].unique()
        hsgt_corr_data = []
        hsgt_labels = []
        
        for indicator in hsgt_indicators:
            indicator_data = summary_df[summary_df['HSGT指标'] == indicator]['相关系数绝对值']
            hsgt_corr_data.append(indicator_data.values)
            # 简化标签
            if 'pct_change' in indicator:
                label = indicator.replace('hsgt_ratio_pct_change_', '百分比变化_').replace('d', '天')
            else:
                label = indicator.replace('hsgt_ratio_change_', '绝对变化_').replace('d', '天')
            hsgt_labels.append(label)
        
        ax3.boxplot(hsgt_corr_data, labels=hsgt_labels)
        ax3.set_title('不同港股通指标的相关性分布')
        ax3.set_ylabel('相关系数绝对值')
        ax3.tick_params(axis='x', rotation=45)
        ax3.grid(True, alpha=0.3)
        
        # 4. 收益类型比较
        ax4 = axes[1, 1]
        return_type_corr = summary_df.groupby('未来收益类型')['相关系数绝对值'].mean()
        bars4 = ax4.bar(return_type_corr.index, return_type_corr.values, alpha=0.7, color='lightgreen')
        ax4.set_title('不同收益类型的平均相关性')
        ax4.set_xlabel('收益类型')
        ax4.set_ylabel('平均相关系数绝对值')
        ax4.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar in bars4:
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                    f'{height:.3f}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        # 保存图片
        filename = os.path.join(self.output_dir, 'correlation_analysis_by_timeframe.png')
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 时间框架相关性分析图已保存: {filename}")
        plt.show()
    
    def plot_scatter_analysis(self, clean_data: pd.DataFrame, top_correlations: list):
        """绘制散点图分析最强相关性"""
        n_plots = min(6, len(top_correlations))  # 最多显示6个最强相关性
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('港股通变化与未来收益散点图分析（最强相关性）', fontsize=16, fontweight='bold')
        
        axes = axes.flatten()
        
        for i in range(n_plots):
            if i < len(top_correlations):
                row = top_correlations.iloc[i]
                hsgt_col = row['HSGT指标']
                return_col = f"{row['未来收益类型']}_{row['前瞻天数']}d"
                
                if hsgt_col in clean_data.columns and return_col in clean_data.columns:
                    # 移除异常值（使用IQR方法）
                    Q1_x = clean_data[hsgt_col].quantile(0.25)
                    Q3_x = clean_data[hsgt_col].quantile(0.75)
                    IQR_x = Q3_x - Q1_x
                    
                    Q1_y = clean_data[return_col].quantile(0.25)
                    Q3_y = clean_data[return_col].quantile(0.75)
                    IQR_y = Q3_y - Q1_y
                    
                    # 过滤异常值
                    mask = (
                        (clean_data[hsgt_col] >= Q1_x - 1.5 * IQR_x) &
                        (clean_data[hsgt_col] <= Q3_x + 1.5 * IQR_x) &
                        (clean_data[return_col] >= Q1_y - 1.5 * IQR_y) &
                        (clean_data[return_col] <= Q3_y + 1.5 * IQR_y)
                    )
                    
                    filtered_data = clean_data[mask]
                    
                    # 绘制散点图
                    axes[i].scatter(filtered_data[hsgt_col], filtered_data[return_col], 
                                  alpha=0.6, s=20)
                    
                    # 添加趋势线
                    z = np.polyfit(filtered_data[hsgt_col], filtered_data[return_col], 1)
                    p = np.poly1d(z)
                    axes[i].plot(filtered_data[hsgt_col], p(filtered_data[hsgt_col]), 
                               "r--", alpha=0.8, linewidth=2)
                    
                    # 设置标题和标签
                    title = f"{hsgt_col.replace('hsgt_ratio_', '').replace('_', ' ')} vs {return_col.replace('_', ' ')}"
                    axes[i].set_title(f"{title}\n相关系数: {row['相关系数']:.3f}", fontsize=10)
                    axes[i].set_xlabel(hsgt_col.replace('hsgt_ratio_', '港股通').replace('_', ' '))
                    axes[i].set_ylabel(return_col.replace('_', ' '))
                    axes[i].grid(True, alpha=0.3)
            else:
                axes[i].set_visible(False)
        
        plt.tight_layout()
        
        # 保存图片
        filename = os.path.join(self.output_dir, 'scatter_analysis_top_correlations.png')
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 散点图分析已保存: {filename}")
        plt.show()
    
    def plot_time_series_example(self, complete_data: pd.DataFrame, sample_stocks: int = 3):
        """绘制时间序列示例"""
        # 选择几只有代表性的股票
        stocks_with_data = complete_data.groupby('code').size()
        top_stocks = stocks_with_data.nlargest(sample_stocks).index.tolist()
        
        fig, axes = plt.subplots(sample_stocks, 1, figsize=(16, 4*sample_stocks))
        if sample_stocks == 1:
            axes = [axes]
        
        fig.suptitle('港股通持股比例与股价时间序列示例', fontsize=16, fontweight='bold')
        
        for i, stock_code in enumerate(top_stocks):
            stock_data = complete_data[complete_data['code'] == stock_code].sort_values('time_key')
            
            if len(stock_data) > 10:  # 确保有足够的数据点
                ax1 = axes[i]
                ax2 = ax1.twinx()
                
                # 绘制股价
                line1 = ax1.plot(stock_data['time_key'], stock_data['close'], 
                               'b-', linewidth=2, label='收盘价')
                ax1.set_ylabel('股价 (HKD)', color='b')
                ax1.tick_params(axis='y', labelcolor='b')
                
                # 绘制港股通持股比例
                line2 = ax2.plot(stock_data['time_key'], stock_data['hsgt_holding_ratio'], 
                               'r-', linewidth=2, label='港股通持股比例')
                ax2.set_ylabel('港股通持股比例 (%)', color='r')
                ax2.tick_params(axis='y', labelcolor='r')
                
                # 设置标题
                stock_name = stock_data['name'].iloc[0] if 'name' in stock_data.columns else stock_code
                ax1.set_title(f'{stock_code} - {stock_name}')
                
                # 设置x轴
                ax1.tick_params(axis='x', rotation=45)
                
                # 添加图例
                lines = line1 + line2
                labels = [l.get_label() for l in lines]
                ax1.legend(lines, labels, loc='upper left')
                
                ax1.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图片
        filename = os.path.join(self.output_dir, 'time_series_examples.png')
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 时间序列示例图已保存: {filename}")
        plt.show()

def load_latest_analysis_results(analysis_dir: str):
    """加载最新的分析结果"""
    if not os.path.exists(analysis_dir):
        print(f"分析结果目录不存在: {analysis_dir}")
        return None, None
    
    # 查找最新的分析文件
    summary_files = [f for f in os.listdir(analysis_dir) if f.startswith('hsgt_correlation_summary_')]
    data_files = [f for f in os.listdir(analysis_dir) if f.startswith('hsgt_complete_analysis_data_')]
    
    if not summary_files or not data_files:
        print("未找到分析结果文件")
        return None, None
    
    # 获取最新文件
    latest_summary = max(summary_files)
    latest_data = max(data_files)
    
    print(f"加载分析结果: {latest_summary}, {latest_data}")
    
    try:
        summary_df = pd.read_csv(os.path.join(analysis_dir, latest_summary))
        complete_data = pd.read_csv(os.path.join(analysis_dir, latest_data))
        complete_data['time_key'] = pd.to_datetime(complete_data['time_key'])
        
        return summary_df, complete_data
    except Exception as e:
        print(f"加载分析结果失败: {e}")
        return None, None

def main():
    """主函数"""
    print("="*80)
    print("           港股通持股比例变化可视化分析")
    print("="*80)
    
    # 设置路径
    script_dir = os.path.dirname(__file__)
    analysis_dir = os.path.join(script_dir, 'analysis_results')
    output_dir = os.path.join(script_dir, 'visualization_results')
    
    # 加载分析结果
    summary_df, complete_data = load_latest_analysis_results(analysis_dir)
    
    if summary_df is None or complete_data is None:
        print("请先运行 hsgt_predictive_analysis.py 生成分析结果")
        return
    
    # 创建可视化分析器
    visualizer = HSGTVisualizationAnalyzer(output_dir)
    
    # 计算相关性矩阵（用于热力图）
    numeric_cols = complete_data.select_dtypes(include=[np.number]).columns
    hsgt_cols = [col for col in numeric_cols if 'hsgt_ratio' in col]
    return_cols = [col for col in numeric_cols if 'return_' in col or 'log_return' in col]
    
    if hsgt_cols and return_cols:
        analysis_cols = hsgt_cols + return_cols
        clean_data = complete_data[analysis_cols].dropna()
        correlation_matrix = clean_data.corr()
        
        # 生成可视化
        print("\n生成可视化图表...")
        
        # 1. 相关性热力图
        visualizer.plot_correlation_heatmap(correlation_matrix)
        
        # 2. 按时间框架的相关性分析
        visualizer.plot_correlation_by_timeframe(summary_df)
        
        # 3. 散点图分析（最强相关性）
        top_correlations = summary_df.head(6)  # 取前6个最强相关性
        visualizer.plot_scatter_analysis(clean_data, top_correlations)
        
        # 4. 时间序列示例
        visualizer.plot_time_series_example(complete_data, sample_stocks=3)
        
        print(f"\n📊 所有可视化图表已保存到: {output_dir}")
    else:
        print("未找到足够的数据进行可视化分析")
    
    print("="*80)

if __name__ == "__main__":
    main()
