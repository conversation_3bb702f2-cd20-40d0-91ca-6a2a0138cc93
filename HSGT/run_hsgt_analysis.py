#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通持股比例变化分析主运行脚本

一键运行所有港股通分析：
1. 预测性分析（相关性分析）
2. 可视化分析
3. 策略回测
"""

import sys
import os
import subprocess
from datetime import datetime

def run_script(script_path: str, script_name: str):
    """运行Python脚本"""
    print(f"\n{'='*60}")
    print(f"正在运行: {script_name}")
    print(f"脚本路径: {script_path}")
    print(f"{'='*60}")
    
    try:
        # 使用当前Python解释器运行脚本
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, 
                              text=True, 
                              cwd=os.path.dirname(script_path))
        
        if result.returncode == 0:
            print(f"✅ {script_name} 运行成功")
            if result.stdout:
                print("输出:")
                print(result.stdout)
        else:
            print(f"❌ {script_name} 运行失败")
            if result.stderr:
                print("错误信息:")
                print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 运行 {script_name} 时发生异常: {e}")
        return False
    
    return True

def check_data_file():
    """检查数据文件是否存在"""
    script_dir = os.path.dirname(__file__)
    project_root = os.path.abspath(os.path.join(script_dir, '..'))
    data_path = os.path.join(project_root, 'data', 'h_shares_daily.parquet')
    
    if os.path.exists(data_path):
        print(f"✅ 数据文件存在: {data_path}")
        return True
    else:
        print(f"❌ 数据文件不存在: {data_path}")
        print("请确保数据文件存在后再运行分析")
        return False

def create_directories():
    """创建必要的目录"""
    script_dir = os.path.dirname(__file__)

    directories = [
        os.path.join(script_dir, 'analysis_results'),
        os.path.join(script_dir, 'visualization_results'),
        os.path.join(script_dir, 'same_period_results'),
        os.path.join(script_dir, 'backtest_results'),
        os.path.join(script_dir, 'enhanced_backtest_results')
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 Directory created/confirmed: {directory}")

def main():
    """主函数"""
    print("="*80)
    print("           港股通持股比例变化分析 - 主运行脚本")
    print("="*80)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查数据文件
    if not check_data_file():
        return
    
    # 创建必要目录
    create_directories()
    
    # 获取脚本目录
    script_dir = os.path.dirname(__file__)
    
    # 定义要运行的脚本
    scripts = [
        {
            'path': os.path.join(script_dir, 'hsgt_predictive_analysis.py'),
            'name': 'HSGT Predictive Analysis',
            'description': 'Analyze correlation between HSGT holdings change and future returns'
        },
        {
            'path': os.path.join(script_dir, 'hsgt_visualization_analysis.py'),
            'name': 'HSGT Visualization Analysis',
            'description': 'Generate correlation analysis charts and visualizations'
        },
        {
            'path': os.path.join(script_dir, 'hsgt_same_period_analysis.py'),
            'name': 'HSGT Same Period Analysis',
            'description': 'Analyze N-day HSGT change vs N-day returns correlation'
        },
        {
            'path': os.path.join(script_dir, 'hsgt_strategy_backtest.py'),
            'name': 'HSGT Strategy Backtest',
            'description': 'Build trading strategy based on HSGT changes and backtest'
        },
        {
            'path': os.path.join(script_dir, 'hsgt_enhanced_strategy_backtest.py'),
            'name': 'HSGT Enhanced Strategy Backtest',
            'description': 'Enhanced strategy backtest with benchmark comparison'
        }
    ]
    
    # 运行分析流程
    success_count = 0
    total_scripts = len(scripts)
    
    for i, script_info in enumerate(scripts, 1):
        print(f"\n🚀 步骤 {i}/{total_scripts}: {script_info['description']}")
        
        if not os.path.exists(script_info['path']):
            print(f"❌ 脚本文件不存在: {script_info['path']}")
            continue
        
        if run_script(script_info['path'], script_info['name']):
            success_count += 1
        else:
            print(f"⚠️  {script_info['name']} 运行失败，但继续执行后续步骤...")
    
    # 总结
    print(f"\n{'='*80}")
    print("                        分析完成总结")
    print(f"{'='*80}")
    print(f"总脚本数: {total_scripts}")
    print(f"成功运行: {success_count}")
    print(f"失败数量: {total_scripts - success_count}")
    print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success_count == total_scripts:
        print("🎉 所有分析步骤都已成功完成！")
        
        # 显示结果文件位置
        print(f"\n📁 分析结果文件位置:")
        print(f"   相关性分析结果: {os.path.join(script_dir, 'analysis_results')}")
        print(f"   可视化图表: {os.path.join(script_dir, 'visualization_results')}")
        print(f"   策略回测结果: {os.path.join(script_dir, 'backtest_results')}")
        
        print(f"\n📊 主要输出文件:")
        print(f"   - hsgt_correlation_summary_*.csv: 相关性分析汇总")
        print(f"   - hsgt_complete_analysis_data_*.csv: 完整分析数据")
        print(f"   - correlation_heatmap.png: 相关性热力图")
        print(f"   - correlation_analysis_by_timeframe.png: 时间框架分析图")
        print(f"   - scatter_analysis_top_correlations.png: 散点图分析")
        print(f"   - time_series_examples.png: 时间序列示例")
        print(f"   - strategy_backtest_performance.png: 策略回测图表")
        print(f"   - backtest_trades_*.csv: 交易记录")
        print(f"   - backtest_portfolio_*.csv: 组合历史")
        print(f"   - backtest_metrics_*.csv: 绩效指标")
        
    else:
        print("⚠️  部分分析步骤失败，请检查错误信息并重新运行相应脚本")
    
    print("="*80)

if __name__ == "__main__":
    main()
