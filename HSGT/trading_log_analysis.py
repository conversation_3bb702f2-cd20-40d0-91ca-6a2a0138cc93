#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通交易日志分析

分析最新的交易记录，展示策略的实际执行情况
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime

# 设置图表样式
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

def analyze_trading_log():
    """分析交易日志"""
    
    print("="*80)
    print("    HSGT Daily Rotation Strategy - Trading Log Analysis")
    print("="*80)
    
    # 读取最新的交易数据
    script_dir = os.path.dirname(__file__)
    trades_file = os.path.join(script_dir, 'daily_rotation_results', 'daily_rotation_trades_20250903_160013.csv')
    portfolio_file = os.path.join(script_dir, 'daily_rotation_results', 'daily_rotation_portfolio_20250903_160013.csv')
    
    try:
        trades_df = pd.read_csv(trades_file)
        portfolio_df = pd.read_csv(portfolio_file)
        
        trades_df['date'] = pd.to_datetime(trades_df['date'])
        portfolio_df['date'] = pd.to_datetime(portfolio_df['date'])
        
        print(f"✅ Trading data loaded successfully")
        print(f"   Trades: {len(trades_df)} records")
        print(f"   Portfolio: {len(portfolio_df)} records")
        print(f"   Period: {trades_df['date'].min().strftime('%Y-%m-%d')} to {trades_df['date'].max().strftime('%Y-%m-%d')}")
        
    except Exception as e:
        print(f"❌ Failed to load trading data: {e}")
        return
    
    # 基本统计
    print(f"\n📊 Strategy Performance Summary:")
    initial_value = 1000000
    final_value = portfolio_df['portfolio_value'].iloc[-1]
    total_return = (final_value / initial_value - 1) * 100
    
    print(f"   Initial Capital: ${initial_value:,.0f}")
    print(f"   Final Value: ${final_value:,.0f}")
    print(f"   Total Return: {total_return:.2f}%")
    print(f"   Total Trades: {len(trades_df)}")
    
    # 计算年化收益率
    start_date = trades_df['date'].min()
    end_date = trades_df['date'].max()
    days = (end_date - start_date).days
    years = days / 365.25
    annualized_return = ((final_value / initial_value) ** (1/years) - 1) * 100
    print(f"   Trading Period: {days} days ({years:.2f} years)")
    print(f"   Annualized Return: {annualized_return:.2f}%")
    
    # 最近交易分析
    print(f"\n🔍 Recent Trading Activity (Last 20 trades):")
    recent_trades = trades_df.tail(20)
    
    print(f"{'Date':<12} {'Stock':<15} {'HSGT Change':<12} {'Return':<10} {'P&L':<15}")
    print("-" * 75)
    
    for _, trade in recent_trades.iterrows():
        pnl_str = f"${trade['pnl']:,.0f}" if not pd.isna(trade['pnl']) else "N/A"
        return_str = f"{trade['next_day_return']*100:.2f}%" if not pd.isna(trade['next_day_return']) else "N/A"
        hsgt_str = f"{trade['hsgt_change']:.2f}%" if not pd.isna(trade['hsgt_change']) else "N/A"
        
        print(f"{trade['date'].strftime('%Y-%m-%d'):<12} {trade['code']:<15} {hsgt_str:<12} {return_str:<10} {pnl_str:<15}")
    
    # 胜率分析
    valid_trades = trades_df[trades_df['next_day_return'].notna()]
    winning_trades = len(valid_trades[valid_trades['next_day_return'] > 0])
    total_valid_trades = len(valid_trades)
    win_rate = (winning_trades / total_valid_trades * 100) if total_valid_trades > 0 else 0
    
    print(f"\n📈 Trading Statistics:")
    print(f"   Win Rate: {win_rate:.2f}% ({winning_trades}/{total_valid_trades})")
    print(f"   Average Return per Trade: {valid_trades['next_day_return'].mean()*100:.3f}%")
    print(f"   Average HSGT Change: {valid_trades['hsgt_change'].mean():.3f}%")
    print(f"   Best Trade: {valid_trades['next_day_return'].max()*100:.2f}%")
    print(f"   Worst Trade: {valid_trades['next_day_return'].min()*100:.2f}%")
    
    # 最活跃股票
    print(f"\n🏆 Most Traded Stocks:")
    stock_counts = trades_df['code'].value_counts().head(10)
    for i, (stock, count) in enumerate(stock_counts.items(), 1):
        stock_name = trades_df[trades_df['code'] == stock]['name'].iloc[0] if 'name' in trades_df.columns else "N/A"
        print(f"   {i:2d}. {stock} ({stock_name}): {count} trades")
    
    # 月度表现
    print(f"\n📅 Monthly Performance:")
    portfolio_df['year_month'] = portfolio_df['date'].dt.to_period('M')
    monthly_performance = []
    
    for period in portfolio_df['year_month'].unique():
        month_data = portfolio_df[portfolio_df['year_month'] == period]
        if len(month_data) > 1:
            start_value = month_data['portfolio_value'].iloc[0]
            end_value = month_data['portfolio_value'].iloc[-1]
            monthly_return = (end_value / start_value - 1) * 100
            monthly_performance.append({
                'month': str(period),
                'return': monthly_return,
                'start_value': start_value,
                'end_value': end_value
            })
    
    monthly_df = pd.DataFrame(monthly_performance)
    if not monthly_df.empty:
        print(f"{'Month':<10} {'Return':<10} {'Start Value':<15} {'End Value':<15}")
        print("-" * 55)
        for _, month in monthly_df.tail(12).iterrows():  # 显示最近12个月
            print(f"{month['month']:<10} {month['return']:>8.2f}% ${month['start_value']:>12,.0f} ${month['end_value']:>12,.0f}")
    
    # 最大回撤分析
    portfolio_values = portfolio_df['portfolio_value'].values
    peak = np.maximum.accumulate(portfolio_values)
    drawdown = (portfolio_values - peak) / peak * 100
    max_drawdown = np.min(drawdown)
    max_drawdown_date = portfolio_df.iloc[np.argmin(drawdown)]['date']
    
    print(f"\n⚠️  Risk Analysis:")
    print(f"   Maximum Drawdown: {max_drawdown:.2f}%")
    print(f"   Max Drawdown Date: {max_drawdown_date.strftime('%Y-%m-%d')}")
    
    # 波动率分析
    daily_returns = portfolio_df['daily_return'].dropna()
    volatility = daily_returns.std()
    annualized_volatility = volatility * np.sqrt(252)
    
    print(f"   Daily Volatility: {volatility:.3f}%")
    print(f"   Annualized Volatility: {annualized_volatility:.2f}%")
    
    # 夏普比率
    risk_free_rate = 3.0  # 假设无风险利率3%
    excess_return = annualized_return - risk_free_rate
    sharpe_ratio = excess_return / annualized_volatility if annualized_volatility > 0 else 0
    print(f"   Sharpe Ratio: {sharpe_ratio:.2f}")
    
    # HSGT变化分布
    print(f"\n🎯 HSGT Change Distribution:")
    hsgt_changes = valid_trades['hsgt_change'].dropna()
    print(f"   Mean HSGT Change: {hsgt_changes.mean():.3f}%")
    print(f"   Median HSGT Change: {hsgt_changes.median():.3f}%")
    print(f"   Min HSGT Change: {hsgt_changes.min():.3f}%")
    print(f"   Max HSGT Change: {hsgt_changes.max():.3f}%")
    print(f"   Std HSGT Change: {hsgt_changes.std():.3f}%")
    
    # 相关性分析
    if len(valid_trades) > 10:
        correlation = valid_trades['hsgt_change'].corr(valid_trades['next_day_return'])
        print(f"\n🔗 HSGT Change vs Next Day Return Correlation: {correlation:.4f}")
    
    # 创建可视化
    create_trading_visualizations(trades_df, portfolio_df, valid_trades)
    
    return trades_df, portfolio_df

def create_trading_visualizations(trades_df, portfolio_df, valid_trades):
    """创建交易可视化图表"""
    
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    fig.suptitle('HSGT Daily Rotation Strategy - Trading Log Analysis', fontsize=16, fontweight='bold')
    
    # 1. 组合价值变化
    ax1 = axes[0, 0]
    ax1.plot(portfolio_df['date'], portfolio_df['portfolio_value'], linewidth=2, color='blue')
    ax1.set_title('Portfolio Value Over Time')
    ax1.set_ylabel('Portfolio Value ($)')
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(axis='x', rotation=45)
    
    # 添加关键里程碑
    milestones = [2000000, 3000000, 4000000, 5000000, 6000000, 7000000]
    for milestone in milestones:
        if portfolio_df['portfolio_value'].max() >= milestone:
            milestone_date = portfolio_df[portfolio_df['portfolio_value'] >= milestone]['date'].iloc[0]
            ax1.axhline(y=milestone, color='red', linestyle='--', alpha=0.5)
            ax1.text(milestone_date, milestone, f'${milestone/1000000:.0f}M', 
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='yellow', alpha=0.7))
    
    # 2. 日收益分布
    ax2 = axes[0, 1]
    daily_returns = portfolio_df['daily_return'].dropna()
    ax2.hist(daily_returns, bins=50, alpha=0.7, edgecolor='black')
    ax2.axvline(x=0, color='red', linestyle='--', alpha=0.7)
    ax2.axvline(x=daily_returns.mean(), color='green', linestyle='-', alpha=0.7, 
               label=f'Mean: {daily_returns.mean():.3f}%')
    ax2.set_title('Daily Return Distribution')
    ax2.set_xlabel('Daily Return (%)')
    ax2.set_ylabel('Frequency')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. HSGT变化 vs 收益散点图
    ax3 = axes[0, 2]
    if not valid_trades.empty:
        ax3.scatter(valid_trades['hsgt_change'], valid_trades['next_day_return']*100, 
                   alpha=0.6, s=20)
        
        # 添加趋势线
        x = valid_trades['hsgt_change'].values
        y = valid_trades['next_day_return'].values * 100
        valid_mask = np.isfinite(x) & np.isfinite(y)
        if valid_mask.sum() > 1:
            z = np.polyfit(x[valid_mask], y[valid_mask], 1)
            p = np.poly1d(z)
            ax3.plot(x[valid_mask], p(x[valid_mask]), "r--", alpha=0.8)
            
            # 计算相关系数
            corr = np.corrcoef(x[valid_mask], y[valid_mask])[0, 1]
            ax3.text(0.05, 0.95, f'Correlation: {corr:.4f}', 
                    transform=ax3.transAxes, bbox=dict(boxstyle="round", facecolor='wheat'))
        
        ax3.set_title('HSGT Change vs Next Day Return')
        ax3.set_xlabel('HSGT Change (%)')
        ax3.set_ylabel('Next Day Return (%)')
        ax3.grid(True, alpha=0.3)
    
    # 4. 月度收益
    ax4 = axes[1, 0]
    portfolio_df['year_month'] = portfolio_df['date'].dt.to_period('M')
    monthly_returns = []
    months = []
    
    for period in sorted(portfolio_df['year_month'].unique()):
        month_data = portfolio_df[portfolio_df['year_month'] == period]
        if len(month_data) > 1:
            start_value = month_data['portfolio_value'].iloc[0]
            end_value = month_data['portfolio_value'].iloc[-1]
            monthly_return = (end_value / start_value - 1) * 100
            monthly_returns.append(monthly_return)
            months.append(str(period))
    
    if monthly_returns:
        colors = ['green' if r > 0 else 'red' for r in monthly_returns]
        bars = ax4.bar(range(len(months)), monthly_returns, color=colors, alpha=0.7)
        ax4.set_title('Monthly Returns')
        ax4.set_ylabel('Monthly Return (%)')
        ax4.set_xticks(range(len(months)))
        ax4.set_xticklabels(months, rotation=45)
        ax4.grid(True, alpha=0.3)
        ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    # 5. 回撤分析
    ax5 = axes[1, 1]
    portfolio_values = portfolio_df['portfolio_value'].values
    peak = np.maximum.accumulate(portfolio_values)
    drawdown = (portfolio_values - peak) / peak * 100
    
    ax5.fill_between(portfolio_df['date'], drawdown, 0, alpha=0.3, color='red')
    ax5.plot(portfolio_df['date'], drawdown, color='red', linewidth=1)
    ax5.set_title('Drawdown Analysis')
    ax5.set_ylabel('Drawdown (%)')
    ax5.grid(True, alpha=0.3)
    ax5.tick_params(axis='x', rotation=45)
    
    # 6. 最活跃股票
    ax6 = axes[1, 2]
    stock_counts = trades_df['code'].value_counts().head(10)
    ax6.barh(range(len(stock_counts)), stock_counts.values, alpha=0.7)
    ax6.set_title('Most Traded Stocks (Top 10)')
    ax6.set_xlabel('Number of Trades')
    ax6.set_yticks(range(len(stock_counts)))
    ax6.set_yticklabels(stock_counts.index)
    ax6.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    output_dir = os.path.join(os.path.dirname(__file__), 'trading_log_analysis')
    os.makedirs(output_dir, exist_ok=True)
    
    filename = os.path.join(output_dir, 'trading_log_analysis.png')
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"\n📊 Trading log analysis chart saved: {filename}")
    plt.show()

def main():
    """主函数"""
    
    # 分析交易日志
    trades_df, portfolio_df = analyze_trading_log()
    
    print("\n" + "="*80)
    print("    Key Insights from Trading Log")
    print("="*80)
    
    if trades_df is not None and portfolio_df is not None:
        # 最新状态
        latest_value = portfolio_df['portfolio_value'].iloc[-1]
        latest_date = portfolio_df['date'].iloc[-1]
        
        print(f"📅 Latest Status (as of {latest_date.strftime('%Y-%m-%d')}):")
        print(f"   Portfolio Value: ${latest_value:,.0f}")
        print(f"   Total Gain: ${latest_value - 1000000:,.0f}")
        
        # 最近表现
        recent_30_days = portfolio_df.tail(30)
        if len(recent_30_days) > 1:
            recent_return = (recent_30_days['portfolio_value'].iloc[-1] / 
                           recent_30_days['portfolio_value'].iloc[0] - 1) * 100
            print(f"   Last 30 Days Return: {recent_return:.2f}%")
        
        # 策略状态
        recent_trades = trades_df.tail(10)
        recent_win_rate = (recent_trades['next_day_return'] > 0).mean() * 100
        print(f"   Recent Win Rate (Last 10 trades): {recent_win_rate:.1f}%")
        
        print(f"\n💡 Strategy is {'performing well' if recent_win_rate > 45 else 'underperforming recently'}")
        print(f"🎯 Continue monitoring HSGT changes for trading opportunities")
    
    print("="*80)

if __name__ == "__main__":
    main()
